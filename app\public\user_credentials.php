<?php
// C:\workapp\app\public\user_credentials.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
require_role(['Admin']);
$me = current_user();

// Kullanıcı adı oluşturma fonksiyonu (import_personnel.php ile aynı)
function generateUsername($fullName, $sicilNo) {
    $name = mb_strtolower(trim($fullName), 'UTF-8');
    $name = str_replace(
        ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'],
        ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'],
        $name
    );
    $name = preg_replace('/[^a-z\s]/', '', $name);
    $parts = explode(' ', $name);
    $username = '';
    
    if (count($parts) >= 2) {
        $username = substr($parts[0], 0, 4) . substr($parts[1], 0, 4);
    } else {
        $username = substr($parts[0], 0, 8);
    }
    
    $username .= substr($sicilNo, -2);
    return $username;
}

// Parola oluşturma fonksiyonu (import_personnel.php ile aynı)
function generatePassword($sicilNo, $firstName) {
    $firstChar = mb_strtoupper(mb_substr($firstName, 0, 1, 'UTF-8'), 'UTF-8');
    return $firstChar . $sicilNo . '!';
}

// Personel dosyasından giriş bilgilerini oluştur
$credentials = [];
$csvFile = __DIR__ . '/../../personel.txt';

if (file_exists($csvFile)) {
    $handle = fopen($csvFile, 'r');
    fgets($handle); // İlk satırı atla
    
    while (($line = fgets($handle)) !== false) {
        $data = str_getcsv($line, "\t");
        
        if (count($data) < 8) continue;
        
        $sicilNo = trim($data[1]);
        $fullName = trim($data[2]);
        $unvanGrubu = trim($data[3]);
        $gorevUnvani = trim($data[4]);
        $mudurluk = trim($data[5]);
        $organizasyon = trim($data[6]);
        
        if (empty($sicilNo) || empty($fullName)) continue;
        
        $username = generateUsername($fullName, $sicilNo);
        $nameParts = explode(' ', $fullName);
        $firstName = $nameParts[0];
        $password = generatePassword($sicilNo, $firstName);
        
        $credentials[] = [
            'sicil_no' => $sicilNo,
            'full_name' => $fullName,
            'username' => $username,
            'password' => $password,
            'unvan_grubu' => $unvanGrubu,
            'gorev_unvani' => $gorevUnvani,
            'birim' => $organizasyon ?: $mudurluk
        ];
    }
    
    fclose($handle);
}

// CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="kullanici_giris_bilgileri.csv"');
    
    $output = fopen('php://output', 'w');
    
    // UTF-8 BOM ekle
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Başlık satırı
    fputcsv($output, ['Sicil No', 'Ad Soyad', 'Kullanıcı Adı', 'Parola', 'Ünvan Grubu', 'Görev Ünvanı', 'Birim'], ';');
    
    // Veriler
    foreach ($credentials as $cred) {
        fputcsv($output, [
            $cred['sicil_no'],
            $cred['full_name'],
            $cred['username'],
            $cred['password'],
            $cred['unvan_grubu'],
            $cred['gorev_unvani'],
            $cred['birim']
        ], ';');
    }
    
    fclose($output);
    exit;
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Kullanıcı Giriş Bilgileri • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{
      --bg-primary: #0f172a;
      --bg-secondary: #1e293b;
      --bg-tertiary: #334155;
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --accent-primary: #3b82f6;
      --accent-success: #10b981;
      --accent-warning: #f59e0b;
      --accent-danger: #ef4444;
      --border: #475569;
      --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
    }
    
    * { box-sizing: border-box; }
    
    body {
      margin: 0;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      color: var(--text-primary);
      font: 15px/1.6 'Segoe UI', system-ui, -apple-system, sans-serif;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .header {
      background: var(--bg-secondary);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border);
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .header-content h1 {
      margin: 0 0 0.5rem;
      font-size: 2rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header-content p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 1.1rem;
    }
    
    .header-actions {
      display: flex;
      gap: 1rem;
      align-items: center;
    }
    
    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      font-size: 0.95rem;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
    }
    
    .btn-primary {
      background: var(--accent-primary);
      color: white;
    }
    
    .btn-primary:hover {
      background: #2563eb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
    
    .btn-success {
      background: var(--accent-success);
      color: white;
    }
    
    .btn-success:hover {
      background: #059669;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }
    
    .card {
      background: var(--bg-secondary);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border);
    }
    
    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }
    
    .stat-card {
      background: var(--bg-primary);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
      border: 1px solid var(--border);
    }
    
    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-primary);
      margin-bottom: 0.5rem;
    }
    
    .stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 12px;
      border: 1px solid var(--border);
      max-height: 600px;
      overflow-y: auto;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      background: var(--bg-primary);
    }
    
    th, td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
      font-size: 0.9rem;
    }
    
    th {
      background: var(--bg-tertiary);
      font-weight: 600;
      color: var(--text-primary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    
    td {
      color: var(--text-secondary);
    }
    
    tr:hover td {
      background: rgba(59, 130, 246, 0.05);
    }
    
    .credential {
      font-family: 'Courier New', monospace;
      background: var(--bg-tertiary);
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.85rem;
    }
    
    .nav-link {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;
    }
    
    .nav-link:hover {
      color: var(--accent-primary);
    }
    
    .search-box {
      width: 100%;
      max-width: 400px;
      padding: 0.75rem 1rem;
      border: 1px solid var(--border);
      border-radius: 12px;
      background: var(--bg-primary);
      color: var(--text-primary);
      font-size: 0.95rem;
      margin-bottom: 1rem;
    }
    
    .search-box:focus {
      outline: none;
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="header-content">
        <h1>Kullanıcı Giriş Bilgileri</h1>
        <p>Tüm personelin sistem giriş bilgileri</p>
        <div style="margin-top: 1rem;">
          <a href="/import_personnel.php" class="nav-link">← Personel İçe Aktarma</a>
        </div>
      </div>
      <div class="header-actions">
        <a href="?export=csv" class="btn btn-success">
          📥 CSV İndir
        </a>
      </div>
    </div>

    <div class="stats">
      <div class="stat-card">
        <div class="stat-number"><?= count($credentials) ?></div>
        <div class="stat-label">Toplam Personel</div>
      </div>
      <div class="stat-card">
        <div class="stat-number"><?= count(array_filter($credentials, fn($c) => in_array(strtolower($c['unvan_grubu']), ['yönetim']))) ?></div>
        <div class="stat-label">Yönetici</div>
      </div>
      <div class="stat-card">
        <div class="stat-number"><?= count(array_filter($credentials, fn($c) => strtolower($c['unvan_grubu']) === 'mühendis')) ?></div>
        <div class="stat-label">Mühendis</div>
      </div>
      <div class="stat-card">
        <div class="stat-number"><?= count(array_filter($credentials, fn($c) => strtolower($c['unvan_grubu']) === 'teknik')) ?></div>
        <div class="stat-label">Teknik Personel</div>
      </div>
    </div>

    <div class="card">
      <input type="text" id="searchBox" class="search-box" placeholder="🔍 Personel ara (ad, sicil no, kullanıcı adı...)">
      
      <div class="table-container">
        <table id="credentialsTable">
          <thead>
            <tr>
              <th>Sicil No</th>
              <th>Ad Soyad</th>
              <th>Kullanıcı Adı</th>
              <th>Parola</th>
              <th>Ünvan Grubu</th>
              <th>Görev Ünvanı</th>
              <th>Birim</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($credentials as $cred): ?>
            <tr>
              <td><?= e($cred['sicil_no']) ?></td>
              <td><?= e($cred['full_name']) ?></td>
              <td><span class="credential"><?= e($cred['username']) ?></span></td>
              <td><span class="credential"><?= e($cred['password']) ?></span></td>
              <td><?= e($cred['unvan_grubu']) ?></td>
              <td><?= e($cred['gorev_unvani']) ?></td>
              <td><?= e($cred['birim']) ?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    // Arama fonksiyonu
    document.getElementById('searchBox').addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const table = document.getElementById('credentialsTable');
      const rows = table.getElementsByTagName('tr');
      
      for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const text = row.textContent.toLowerCase();
        
        if (text.includes(searchTerm)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      }
    });
  </script>
</body>
</html>
