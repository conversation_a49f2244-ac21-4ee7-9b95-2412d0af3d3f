<?php
require __DIR__.'/../inc/db.php';

echo "<pre>";

// Add sicil_no column to Workers table if it doesn't exist
try {
    // First check if the column exists
    $columns = $pdo->query("PRAGMA table_info(Workers)")->fetchAll(PDO::FETCH_ASSOC);
    $columnExists = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'sicil_no') {
            $columnExists = true;
            break;
        }
    }
    
    if (!$columnExists) {
        $pdo->exec('ALTER TABLE Workers ADD COLUMN sicil_no TEXT');
        echo "Sicil_no column added successfully to Workers table.\n";
    } else {
        echo "Sicil_no column already exists in Workers table.\n";
    }
} catch (Exception $e) {
    echo "Error checking/adding sicil_no column: " . $e->getMessage() . "\n";
}

// Add some sample workers
try {
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO Workers(id, sicil_no, full_name, trade, is_active) VALUES 
        (1, 'S001', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>k', 1),
        (2, 'S002', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 1),
        (3, 'S003', '<PERSON>y<PERSON>e De<PERSON>', '<PERSON>n<PERSON>aat', 1),
        (4, 'S004', '<PERSON>ma <PERSON><PERSON>n', '<PERSON><PERSON>trik', 1),
        (5, '<PERSON>005', '<PERSON> <PERSON><PERSON>k', '<PERSON><PERSON>ik', 1),
        (6, '<PERSON>006', '<PERSON>ine Yıldız', '<PERSON>n<PERSON>aat', 1),
        (7, 'S007', '<PERSON> <PERSON>zkan', 'Elektrik', 1),
        (8, 'S008', 'Hatice Koç', 'Mekanik', 1),
        (9, 'S009', 'İsmail Aksoy', 'İnşaat', 1),
        (10, 'S010', 'Zeynep Güler', 'Elektrik', 1),
        (11, 'S011', 'Hasan Aydın', 'Mekanik', 1),
        (12, 'S012', 'Burcu Özdemir', 'İnşaat', 1),
        (13, 'S013', 'Murat Karadağ', 'Elektrik', 1),
        (14, 'S014', 'Selin Taş', 'Mekanik', 1),
        (15, 'S015', 'Canan Çetin', 'İnşaat', 1)");
    $stmt->execute();
    echo "Sample workers added successfully.\n";
} catch (Exception $e) {
    echo "Error adding sample workers: " . $e->getMessage() . "\n";
}

echo "</pre>";
echo "<p><a href='/'>Ana Sayfa</a></p>";