<?php
// C:\workapp\app\public\import_personnel.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
require_role(['Admin']);
$me = current_user();

// Rol eşleştirme fonksiyonu
function mapRole($unvanGrubu, $gorevUnvani) {
    $unvan = strtolower($gorevUnvani);
    $grup = strtolower($unvanGrubu);
    
    // Yönetim rolleri
    if (strpos($unvan, 'direktör') !== false || 
        strpos($unvan, 'müdür') !== false || 
        strpos($unvan, 'birim yöneticisi') !== false ||
        $grup === 'yönetim') {
        return 2; // Manager
    }
    
    // Mühendis rolleri
    if ($grup === 'mühendis' || 
        strpos($unvan, 'mühendis') !== false ||
        strpos($unvan, 'takım lideri') !== false ||
        strpos($unvan, 'sorumlu') !== false) {
        return 3; // Engineer
    }
    
    // Uzman rolleri
    if ($grup === 'uzman' || 
        strpos($unvan, 'uzman') !== false) {
        return 3; // Engineer
    }
    
    // Teknik personel
    if ($grup === 'teknik' || 
        strpos($unvan, 'teknisyen') !== false ||
        strpos($unvan, 'usta') !== false) {
        return 4; // Worker
    }
    
    // Genel hizmet
    if ($grup === 'genel hizmet') {
        return 4; // Worker
    }
    
    // Varsayılan olarak Worker
    return 4;
}

// Kullanıcı adı oluşturma fonksiyonu
function generateUsername($fullName, $sicilNo) {
    // Ad soyadı temizle ve küçük harfe çevir
    $name = mb_strtolower(trim($fullName), 'UTF-8');
    
    // Türkçe karakterleri değiştir
    $name = str_replace(
        ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'],
        ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'],
        $name
    );
    
    // Sadece harfleri al
    $name = preg_replace('/[^a-z\s]/', '', $name);
    
    // Boşlukları kaldır ve ilk 2 kelimeyi al
    $parts = explode(' ', $name);
    $username = '';
    
    if (count($parts) >= 2) {
        $username = substr($parts[0], 0, 4) . substr($parts[1], 0, 4);
    } else {
        $username = substr($parts[0], 0, 8);
    }
    
    // Sicil numarasının son 2 hanesini ekle
    $username .= substr($sicilNo, -2);
    
    return $username;
}

// Parola oluşturma fonksiyonu
function generatePassword($sicilNo, $firstName) {
    // İlk ismin ilk harfi büyük + sicil no + !
    $firstChar = mb_strtoupper(mb_substr($firstName, 0, 1, 'UTF-8'), 'UTF-8');
    return $firstChar . $sicilNo . '!';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_personnel'])) {
    $csvFile = __DIR__ . '/../../personel.txt';
    
    if (!file_exists($csvFile)) {
        $error = "personel.txt dosyası bulunamadı!";
    } else {
        $handle = fopen($csvFile, 'r');
        $imported = 0;
        $errors = [];
        
        // İlk satırı atla (başlık)
        fgets($handle);
        
        $pdo->beginTransaction();
        
        try {
            while (($line = fgets($handle)) !== false) {
                $data = str_getcsv($line, "\t");
                
                if (count($data) < 8) continue;
                
                $sicilNo = trim($data[1]);
                $fullName = trim($data[2]);
                $unvanGrubu = trim($data[3]);
                $gorevUnvani = trim($data[4]);
                $mudurluk = trim($data[5]);
                $organizasyon = trim($data[6]);
                
                if (empty($sicilNo) || empty($fullName)) continue;
                
                // Rol belirle
                $roleId = mapRole($unvanGrubu, $gorevUnvani);
                
                // Kullanıcı adı oluştur
                $username = generateUsername($fullName, $sicilNo);
                
                // İlk ismi al
                $nameParts = explode(' ', $fullName);
                $firstName = $nameParts[0];
                
                // Parola oluştur
                $password = generatePassword($sicilNo, $firstName);
                $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                
                // Birim belirle
                $unit = $organizasyon ?: $mudurluk;
                
                // Kullanıcıyı ekle
                $stmt = $pdo->prepare("INSERT OR IGNORE INTO Users(display_name, username, password_hash, role_id, unit, is_active) VALUES(?, ?, ?, ?, ?, 1)");
                $result = $stmt->execute([$fullName, $username, $passwordHash, $roleId, $unit]);
                
                if ($result && $stmt->rowCount() > 0) {
                    $imported++;
                    
                    // Workers tablosuna da ekle (eğer teknik personelse)
                    if ($roleId == 4) {
                        $stmt2 = $pdo->prepare("INSERT OR IGNORE INTO Workers(full_name, trade, sicil_no, is_active) VALUES(?, ?, ?, 1)");
                        $stmt2->execute([$fullName, $unvanGrubu, $sicilNo]);
                    }
                }
            }
            
            $pdo->commit();
            $success = "$imported kişi başarıyla içe aktarıldı!";
            
        } catch (Exception $e) {
            $pdo->rollback();
            $error = "Hata: " . $e->getMessage();
        }
        
        fclose($handle);
    }
}

// Mevcut kullanıcıları listele
$stmt = $pdo->prepare("SELECT u.*, r.name as role_name FROM Users u JOIN Roles r ON r.id = u.role_id ORDER BY u.display_name");
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Personel İçe Aktarma • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{
      --bg-primary: #0f172a;
      --bg-secondary: #1e293b;
      --bg-tertiary: #334155;
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --accent-primary: #3b82f6;
      --accent-success: #10b981;
      --accent-warning: #f59e0b;
      --accent-danger: #ef4444;
      --border: #475569;
      --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
    }
    
    * { box-sizing: border-box; }
    
    body {
      margin: 0;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      color: var(--text-primary);
      font: 15px/1.6 'Segoe UI', system-ui, -apple-system, sans-serif;
      min-height: 100vh;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    
    .header {
      background: var(--bg-secondary);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border);
    }
    
    .header h1 {
      margin: 0 0 0.5rem;
      font-size: 2rem;
      font-weight: 700;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .header p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 1.1rem;
    }
    
    .card {
      background: var(--bg-secondary);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow);
      border: 1px solid var(--border);
    }
    
    .card h2 {
      margin: 0 0 1.5rem;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      font-size: 0.95rem;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
    }
    
    .btn-primary {
      background: var(--accent-primary);
      color: white;
    }
    
    .btn-primary:hover {
      background: #2563eb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }
    
    .btn-success {
      background: var(--accent-success);
      color: white;
    }
    
    .btn-success:hover {
      background: #059669;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }
    
    .alert {
      padding: 1rem 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;
      font-weight: 500;
    }
    
    .alert-success {
      background: rgba(16, 185, 129, 0.1);
      color: var(--accent-success);
      border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .alert-error {
      background: rgba(239, 68, 68, 0.1);
      color: var(--accent-danger);
      border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 12px;
      border: 1px solid var(--border);
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      background: var(--bg-primary);
    }
    
    th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
    }
    
    th {
      background: var(--bg-tertiary);
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    td {
      color: var(--text-secondary);
    }
    
    tr:hover td {
      background: rgba(59, 130, 246, 0.05);
    }
    
    .badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 6px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .badge-manager {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-success);
    }
    
    .badge-engineer {
      background: rgba(59, 130, 246, 0.2);
      color: var(--accent-primary);
    }
    
    .badge-worker {
      background: rgba(245, 158, 11, 0.2);
      color: var(--accent-warning);
    }
    
    .badge-admin {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-danger);
    }
    
    .nav-link {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;
    }
    
    .nav-link:hover {
      color: var(--accent-primary);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Personel İçe Aktarma</h1>
      <p>personel.txt dosyasından kullanıcıları sisteme aktarın</p>
      <div style="margin-top: 1rem;">
        <a href="/requests.php" class="nav-link">← Ana Sayfa</a>
      </div>
    </div>

    <?php if (isset($success)): ?>
      <div class="alert alert-success">
        ✅ <?= e($success) ?>
      </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
      <div class="alert alert-error">
        ❌ <?= e($error) ?>
      </div>
    <?php endif; ?>

    <div class="card">
      <h2>Personel Verilerini İçe Aktar</h2>
      <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">
        Bu işlem personel.txt dosyasındaki tüm personeli sisteme ekleyecektir. 
        Kullanıcı adları ve parolalar otomatik oluşturulacaktır.
      </p>
      
      <form method="post">
        <button type="submit" name="import_personnel" class="btn btn-success">
          📥 Personeli İçe Aktar
        </button>
      </form>
    </div>

    <div class="card">
      <h2>Mevcut Kullanıcılar (<?= count($users) ?>)</h2>
      
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Ad Soyad</th>
              <th>Kullanıcı Adı</th>
              <th>Rol</th>
              <th>Birim</th>
              <th>Durum</th>
            </tr>
          </thead>
          <tbody>
            <?php foreach ($users as $user): ?>
            <tr>
              <td><?= e($user['display_name']) ?></td>
              <td><code><?= e($user['username']) ?></code></td>
              <td>
                <span class="badge badge-<?= strtolower($user['role_name']) ?>">
                  <?= e($user['role_name']) ?>
                </span>
              </td>
              <td><?= e($user['unit'] ?: '-') ?></td>
              <td>
                <?php if ($user['is_active']): ?>
                  <span style="color: var(--accent-success);">✅ Aktif</span>
                <?php else: ?>
                  <span style="color: var(--text-muted);">❌ Pasif</span>
                <?php endif; ?>
              </td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
</html>
