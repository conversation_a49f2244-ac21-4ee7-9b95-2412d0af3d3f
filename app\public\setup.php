<?php
// C:\workapp\app\public\setup.php
require __DIR__.'/../inc/db.php';

// Check if this is a request to add the new status only
if (isset($_GET['add_status'])) {
    try {
        // Insert the new status
        $pdo->prepare("INSERT OR IGNORE INTO Status(id,code,label) VALUES(8,'PENDING_COMPLETION','Tamamlama Onayı Bekliyor')")->execute();
        echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px}</style>";
        echo "<h3>OK — yeni durum eklendi.</h3>";
        echo "<p><a href='/requests.php'>Ana sayfaya dön</a></p>";
        exit;
    } catch (Exception $e) {
        http_response_code(500);
        echo "Hata: " . htmlspecialchars($e->getMessage());
        exit;
    }
}

$sql = <<<SQL
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA busy_timeout = 5000;

CREATE TABLE IF NOT EXISTS Roles (id INTEGER PRIMARY KEY, name TEXT UNIQUE NOT NULL);
INSERT OR IGNORE INTO Roles(id,name) VALUES (1,'Admin'),(2,'Manager'),(3,'Engineer'),(4,'Worker');

CREATE TABLE IF NOT EXISTS Users (
  id INTEGER PRIMARY KEY,
  display_name TEXT NOT NULL,
  username TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  role_id INTEGER NOT NULL REFERENCES Roles(id),
  unit TEXT,
  is_active INTEGER NOT NULL DEFAULT 1
);

CREATE TABLE IF NOT EXISTS Status (
  id INTEGER PRIMARY KEY,
  code  TEXT UNIQUE NOT NULL,
  label TEXT NOT NULL
);
-- Dil birliği: Müdür -> Yönetici
INSERT OR IGNORE INTO Status(id,code,label) VALUES
 (1,'NEW','Yeni'),
 (2,'ENG_REVIEW','Mühendis İncelemede'), -- dursun, geçmiş uyumluluk için
 (3,'PENDING_MANAGER','Yönetici Onayı Bekliyor'),
 (4,'APPROVED','Yönetici Onayladı'),
 (5,'IN_PROGRESS','Devam Ediyor'),
 (6,'DONE','Tamamlandı'),
 (7,'REJECTED','Reddedildi'),
 (8,'PENDING_COMPLETION','Tamamlama Onayı Bekliyor');

CREATE TABLE IF NOT EXISTS Priorities (
  id INTEGER PRIMARY KEY,
  name TEXT UNIQUE NOT NULL
);
INSERT OR IGNORE INTO Priorities(id,name) VALUES
 (1,'Low'),(2,'Medium'),(3,'High'),(4,'Urgent');

CREATE TABLE IF NOT EXISTS Requests (
  id INTEGER PRIMARY KEY,
  created_at TEXT NOT NULL,
  created_by INTEGER NOT NULL REFERENCES Users(id),
  title TEXT NOT NULL,
  description TEXT,
  unit TEXT,
  priority_id INTEGER NOT NULL REFERENCES Priorities(id),
  status_id INTEGER NOT NULL REFERENCES Status(id),
  assigned_engineer INTEGER REFERENCES Users(id),
  manager_id INTEGER REFERENCES Users(id),
  approved_at TEXT,
  planned_start TEXT,
  planned_finish TEXT,
  actual_start TEXT,
  actual_finish TEXT,
  call_number TEXT
);

CREATE TABLE IF NOT EXISTS Workers (
  id INTEGER PRIMARY KEY,
  full_name TEXT NOT NULL,
  trade TEXT,
  sicil_no TEXT,
  is_active INTEGER NOT NULL DEFAULT 1
);

CREATE TABLE IF NOT EXISTS WorkerAssignments (
  id INTEGER PRIMARY KEY,
  request_id INTEGER NOT NULL REFERENCES Requests(id),
  worker_id  INTEGER NOT NULL REFERENCES Workers(id),
  date TEXT NOT NULL,
  hours REAL NOT NULL CHECK (hours>=0),
  note TEXT
);

CREATE TABLE IF NOT EXISTS Materials (
  id INTEGER PRIMARY KEY,
  code TEXT,
  name TEXT NOT NULL,
  unit TEXT NOT NULL,
  discipline TEXT
);

CREATE TABLE IF NOT EXISTS MaterialUsage (
  id INTEGER PRIMARY KEY,
  request_id  INTEGER NOT NULL REFERENCES Requests(id),
  material_id INTEGER NOT NULL REFERENCES Materials(id),
  quantity REAL NOT NULL CHECK (quantity>=0),
  unit TEXT NOT NULL,
  note TEXT
);

CREATE TABLE IF NOT EXISTS StatusLog (
  id INTEGER PRIMARY KEY,
  request_id INTEGER NOT NULL REFERENCES Requests(id),
  old_status_id INTEGER,
  new_status_id INTEGER NOT NULL,
  changed_by INTEGER NOT NULL REFERENCES Users(id),
  changed_at TEXT NOT NULL,
  note TEXT
);

CREATE TABLE IF NOT EXISTS ChangeLog (
  id INTEGER PRIMARY KEY,
  request_id INTEGER NOT NULL REFERENCES Requests(id),
  field_name TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  changed_by INTEGER NOT NULL REFERENCES Users(id),
  changed_at TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS RequestResources (
  id INTEGER PRIMARY KEY,
  request_id INTEGER NOT NULL REFERENCES Requests(id),
  worker_id INTEGER REFERENCES Workers(id),
  material_id INTEGER REFERENCES Materials(id),
  estimated_hours REAL,
  estimated_quantity REAL,
  unit TEXT,
  note TEXT
);

-- Create discipline table for materials
CREATE TABLE IF NOT EXISTS Disciplines (
  id INTEGER PRIMARY KEY,
  name TEXT UNIQUE NOT NULL
);
INSERT OR IGNORE INTO Disciplines(id,name) VALUES
 (1,'Elektrik'),
 (2,'Mekanik'),
 (3,'İnşaat');
SQL;

$pdo->exec($sql);
echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px}</style>";
echo "<h3>OK — tablo yapısı hazır.</h3>";
echo "<p><a href='/seed.php'>Devam: Seed (örnek kullanıcı/işçi/malzeme ekle)</a></p>";
echo "<p><a href='/setup.php?add_status=1'>Yeni durum ekle (PENDING_COMPLETION)</a></p>";
?>