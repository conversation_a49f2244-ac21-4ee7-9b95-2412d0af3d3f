<?php
// C:\workapp\app\inc\db.php
$path = __DIR__ . '/../storage/database.sqlite'; // Veritabanı dosyası buraya açılacak
$dsn = 'sqlite:' . $path;

try {
    $pdo = new PDO($dsn);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // SQLite için performans ve eşzamanlı erişim ayarları
    $pdo->exec('PRAGMA journal_mode = WAL;');
    $pdo->exec('PRAGMA synchronous = NORMAL;');
    $pdo->exec('PRAGMA busy_timeout = 5000;');
} catch (Exception $e) {
    http_response_code(500);
    echo "Veritabanı bağlantı hatası: " . htmlspecialchars($e->getMessage());
    exit;
}
