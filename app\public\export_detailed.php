<?php
// Export detailed request data including resource usage to CSV
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

// Only allow Admin and Manager roles to export
if (!in_array($me['role'], ['Admin', 'Manager'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=detailed_requests_export_' . date('Y-m-d_H-i-s') . '.csv');

// Open output stream
$output = fopen('php://output', 'w');

// Add BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Export header section
fputcsv($output, ['Detaylı Talep Raporu - Oluşturulma Tarihi: ' . date('Y-m-d H:i:s')]);
fputcsv($output, []);

// Export requests data
fputcsv($output, ['Talep Bilgileri']);
fputcsv($output, [
    'ID', 'Başlık', 'Açıklama', 'Birim', 'Çağrı No', 'Öncelik', 'Durum', 
    'Talep Sahibi', 'Atanan Mühendis', 'Onaylayan Yönetici', 'Oluşturulma Tarihi',
    'Onay Tarihi', 'Planlanan Başlangıç', 'Planlanan Bitiş', 
    'Gerçek Başlangıç', 'Gerçek Bitiş'
]);

$stmt = $pdo->prepare("SELECT 
    r.id, r.title, r.description, r.unit, r.call_number,
    p.name as priority, s.label as status,
    u.display_name as creator, 
    eng.display_name as assigned_engineer,
    mgr.display_name as manager,
    r.created_at, r.approved_at, 
    r.planned_start, r.planned_finish, 
    r.actual_start, r.actual_finish
FROM Requests r
LEFT JOIN Priorities p ON r.priority_id = p.id
LEFT JOIN Status s ON r.status_id = s.id
LEFT JOIN Users u ON r.created_by = u.id
LEFT JOIN Users eng ON r.assigned_engineer = eng.id
LEFT JOIN Users mgr ON r.manager_id = mgr.id
ORDER BY r.id");

$stmt->execute();
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($requests as $request) {
    fputcsv($output, [
        $request['id'], $request['title'], $request['description'], 
        $request['unit'], $request['call_number'], $request['priority'], 
        $request['status'], $request['creator'], $request['assigned_engineer'], 
        $request['manager'], $request['created_at'], $request['approved_at'], 
        $request['planned_start'], $request['planned_finish'], 
        $request['actual_start'], $request['actual_finish']
    ]);
}

fputcsv($output, []); // Empty row
fputcsv($output, []); // Empty row

// Export worker assignments
fputcsv($output, ['İşçi Atamaları']);
fputcsv($output, ['Talep ID', 'Talep Başlığı', 'İşçi Adı', 'Tarih', 'Saat', 'Not']);

$stmt = $pdo->prepare("SELECT 
    r.id as request_id, r.title as request_title,
    w.full_name as worker_name, wa.date, wa.hours, wa.note
FROM WorkerAssignments wa
JOIN Requests r ON wa.request_id = r.id
JOIN Workers w ON wa.worker_id = w.id
ORDER BY r.id, wa.date");

$stmt->execute();
$worker_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($worker_assignments as $assignment) {
    fputcsv($output, [
        $assignment['request_id'], $assignment['request_title'],
        $assignment['worker_name'], $assignment['date'], 
        $assignment['hours'], $assignment['note']
    ]);
}

fputcsv($output, []); // Empty row
fputcsv($output, []); // Empty row

// Export material usage
fputcsv($output, ['Malzeme Kullanımı']);
fputcsv($output, ['Talep ID', 'Talep Başlığı', 'Malzeme Kodu', 'Malzeme Adı', 'Miktar', 'Birim', 'Not']);

$stmt = $pdo->prepare("SELECT 
    r.id as request_id, r.title as request_title,
    m.code as material_code, m.name as material_name,
    mu.quantity, mu.unit, mu.note
FROM MaterialUsage mu
JOIN Requests r ON mu.request_id = r.id
JOIN Materials m ON mu.material_id = m.id
ORDER BY r.id, m.name");

$stmt->execute();
$material_usage = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($material_usage as $usage) {
    fputcsv($output, [
        $usage['request_id'], $usage['request_title'],
        $usage['material_code'], $usage['material_name'],
        $usage['quantity'], $usage['unit'], $usage['note']
    ]);
}

fclose($output);
exit;
?>