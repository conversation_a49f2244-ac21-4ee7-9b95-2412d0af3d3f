<?php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

if ($_SERVER['REQUEST_METHOD']==='POST') {
  $u = trim($_POST['username'] ?? '');
  $p = $_POST['password'] ?? '';
  $stmt = $pdo->prepare("SELECT Users.id, display_name, username, password_hash, Roles.name role
                         FROM Users JOIN Roles ON Roles.id=Users.role_id
                         WHERE username=? AND is_active=1");
  $stmt->execute([$u]);
  $row = $stmt->fetch(PDO::FETCH_ASSOC);
  if ($row && password_verify($p, $row['password_hash'])) {
    $_SESSION['uid']  = $row['id'];
    $_SESSION['name'] = $row['display_name'];
    $_SESSION['role'] = $row['role'];
    header('Location: /requests.php'); exit;
  }
  $err = "Kullanıcı adı veya parola hatalı.";
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Giriş • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="/assets/css/workapp-modern.css">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: var(--space-xl);
    }

    .login-container {
      width: 100%;
      max-width: 420px;
    }

    .login-card {
      background: rgba(30, 41, 59, 0.95);
      backdrop-filter: blur(20px) saturate(180%);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-xl);
      padding: var(--space-2xl);
      box-shadow: var(--shadow-xl);
      position: relative;
      overflow: hidden;
    }

    .login-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--gradient-primary);
    }

    .login-title {
      margin: 0 0 var(--space-xl);
      font-size: var(--font-size-3xl);
      font-weight: 700;
      text-align: center;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .login-form .form-group {
      margin-bottom: var(--space-xl);
    }

    .login-form .form-label {
      margin-bottom: var(--space-sm);
      font-weight: 500;
    }

    .login-form .form-input {
      padding: var(--space-lg);
      font-size: var(--font-size-base);
    }

    .login-btn {
      width: 100%;
      padding: var(--space-lg);
      font-size: var(--font-size-base);
      margin-top: var(--space-md);
    }

    .demo-info {
      margin-top: var(--space-xl);
      padding: var(--space-lg);
      background: var(--bg-primary);
      border-radius: var(--radius-md);
      border: 1px solid var(--border-light);
    }

    .demo-title {
      color: var(--text-primary);
      font-weight: 600;
      margin-bottom: var(--space-sm);
      font-size: var(--font-size-sm);
    }

    .demo-accounts {
      display: flex;
      flex-direction: column;
      gap: var(--space-sm);
    }

    .demo-account {
      display: flex;
      align-items: center;
      gap: var(--space-sm);
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
    }

    .demo-account code {
      background: var(--bg-tertiary);
      padding: var(--space-xs) var(--space-sm);
      border-radius: var(--radius-sm);
      font-family: 'Courier New', monospace;
      font-size: var(--font-size-xs);
    }
  </style>
</head>
<body>
  <div class="login-container">
    <form class="login-card" method="post" autocomplete="off">
      <h1 class="login-title">🚀 WorkApp</h1>

      <?php if(!empty($err)): ?>
        <div class="alert alert-danger">
          ❌ <?=htmlspecialchars($err)?>
        </div>
      <?php endif; ?>

      <div class="login-form">
        <div class="form-group">
          <label class="form-label">👤 Kullanıcı Adı</label>
          <input class="form-input" name="username" required autofocus placeholder="Kullanıcı adınızı girin">
        </div>

        <div class="form-group">
          <label class="form-label">🔒 Parola</label>
          <input class="form-input" type="password" name="password" required placeholder="Parolanızı girin">
        </div>

        <button type="submit" class="btn btn-primary login-btn">
          🚀 Giriş Yap
        </button>
      </div>

      <div class="demo-info">
        <div class="demo-title">🎯 Demo Hesaplar</div>
        <div class="demo-accounts">
          <div class="demo-account">
            👨‍💼 Admin: <code>admin</code> / <code>Admin!23</code>
          </div>
          <div class="demo-account">
            👔 Müdür: <code>mudur</code> / <code>Mudur!23</code>
          </div>
          <div class="demo-account">
            🔧 Mühendis: <code>muhendis</code> / <code>Muhendis!23</code>
          </div>
        </div>
      </div>
    </form>
  </div>
</body>
</html>
