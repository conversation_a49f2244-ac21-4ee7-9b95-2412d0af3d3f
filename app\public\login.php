<?php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

if ($_SERVER['REQUEST_METHOD']==='POST') {
  $u = trim($_POST['username'] ?? '');
  $p = $_POST['password'] ?? '';
  $stmt = $pdo->prepare("SELECT Users.id, display_name, username, password_hash, Roles.name role
                         FROM Users JOIN Roles ON Roles.id=Users.role_id
                         WHERE username=? AND is_active=1");
  $stmt->execute([$u]);
  $row = $stmt->fetch(PDO::FETCH_ASSOC);
  if ($row && password_verify($p, $row['password_hash'])) {
    $_SESSION['uid']  = $row['id'];
    $_SESSION['name'] = $row['display_name'];
    $_SESSION['role'] = $row['role'];
    header('Location: /requests.php'); exit;
  }
  $err = "Kullanıcı adı veya parola hatalı.";
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Giriş • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--card:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;}
    *{box-sizing:border-box} body{margin:0;background:linear-gradient(135deg,#0b1220,#111827);color:var(--text);font:15px/1.5 system-ui,Segoe UI,Arial}
    .wrap{min-height:100dvh;display:grid;place-items:center;padding:24px}
    .card{width:100%;max-width:420px;background:rgba(15,23,42,.85);backdrop-filter:saturate(180%) blur(10px);
      border:1px solid #1f2937;border-radius:14px;padding:24px;box-shadow:0 10px 30px rgba(0,0,0,.35)}
    h1{margin:0 0 18px;font-size:20px}
    label{display:block;margin:10px 0;color:#cbd5e1}
    input{width:100%;padding:12px 14px;border:1px solid #243042;border-radius:10px;background:#0b1220;color:var(--text)}
    input:focus{outline:2px solid #334155;border-color:#475569}
    button{margin-top:14px;width:100%;padding:12px 14px;border:0;border-radius:10px;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    .muted{color:var(--muted);font-size:12px;margin-top:8px}
    .err{background:#7f1d1d;color:#fecaca;border:1px solid #991b1b;padding:10px;border-radius:10px;margin-bottom:12px}
  </style>
</head>
<body>
<div class="wrap">
  <form class="card" method="post" autocomplete="off">
    <h1>WorkApp • Giriş</h1>
    <?php if(!empty($err)): ?><div class="err"><?=htmlspecialchars($err)?></div><?php endif; ?>
    <label>Kullanıcı Adı
      <input name="username" required autofocus>
    </label>
    <label>Parola
      <input type="password" name="password" required>
    </label>
    <button>Giriş Yap</button>
    <div class="muted">Demo kullanıcılar: admin / Admin!23 • mudur / Mudur!23 • muhendis / Muhendis!23</div>
  </form>
</div>
</body>
</html>
