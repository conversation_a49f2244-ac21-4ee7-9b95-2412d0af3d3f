<?php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

if ($_SERVER['REQUEST_METHOD']==='POST') {
  $u = trim($_POST['username'] ?? '');
  $p = $_POST['password'] ?? '';
  $stmt = $pdo->prepare("SELECT Users.id, display_name, username, password_hash, Roles.name role
                         FROM Users JOIN Roles ON Roles.id=Users.role_id
                         WHERE username=? AND is_active=1");
  $stmt->execute([$u]);
  $row = $stmt->fetch(PDO::FETCH_ASSOC);
  if ($row && password_verify($p, $row['password_hash'])) {
    $_SESSION['uid']  = $row['id'];
    $_SESSION['name'] = $row['display_name'];
    $_SESSION['role'] = $row['role'];
    header('Location: /requests.php'); exit;
  }
  $err = "<PERSON>llanıcı adı veya parola hatalı.";
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Giriş • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{
      --bg-primary: #0f172a;
      --bg-secondary: #1e293b;
      --bg-tertiary: #334155;
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --accent-primary: #3b82f6;
      --accent-success: #10b981;
      --accent-danger: #ef4444;
      --border: #475569;
      --shadow: 0 20px 50px -12px rgba(0, 0, 0, 0.4);
    }

    * { box-sizing: border-box; }

    body {
      margin: 0;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
      color: var(--text-primary);
      font: 15px/1.6 'Segoe UI', system-ui, -apple-system, sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
    }

    .wrap {
      width: 100%;
      max-width: 420px;
    }

    .card {
      background: rgba(30, 41, 59, 0.95);
      backdrop-filter: blur(20px) saturate(180%);
      border: 1px solid var(--border);
      border-radius: 20px;
      padding: 3rem;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    }

    h1 {
      margin: 0 0 2rem;
      font-size: 2rem;
      font-weight: 700;
      text-align: center;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    label {
      display: block;
      margin: 1.5rem 0 0.5rem;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.95rem;
    }

    input {
      width: 100%;
      padding: 1rem 1.25rem;
      border: 1px solid var(--border);
      border-radius: 12px;
      background: var(--bg-primary);
      color: var(--text-primary);
      font-size: 1rem;
      transition: all 0.2s ease;
    }

    input:focus {
      outline: none;
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      transform: translateY(-1px);
    }

    button {
      margin-top: 2rem;
      width: 100%;
      padding: 1rem 1.25rem;
      border: none;
      border-radius: 12px;
      background: linear-gradient(135deg, var(--accent-primary), #2563eb);
      color: white;
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    button:active {
      transform: translateY(0);
    }

    .muted {
      color: var(--text-muted);
      font-size: 0.85rem;
      margin-top: 1.5rem;
      text-align: center;
      line-height: 1.5;
    }

    .err {
      background: rgba(239, 68, 68, 0.1);
      color: var(--accent-danger);
      border: 1px solid rgba(239, 68, 68, 0.2);
      padding: 1rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;
      font-weight: 500;
    }
  </style>
</head>
<body>
<div class="wrap">
  <form class="card" method="post" autocomplete="off">
    <h1>WorkApp</h1>
    <?php if(!empty($err)): ?><div class="err">❌ <?=htmlspecialchars($err)?></div><?php endif; ?>

    <label>Kullanıcı Adı</label>
    <input name="username" required autofocus placeholder="Kullanıcı adınızı girin">

    <label>Parola</label>
    <input type="password" name="password" required placeholder="Parolanızı girin">

    <button type="submit">🚀 Giriş Yap</button>

    <div class="muted">
      <strong>Demo Hesaplar:</strong><br>
      👨‍💼 <code>admin</code> / <code>Admin!23</code><br>
      👔 <code>mudur</code> / <code>Mudur!23</code><br>
      🔧 <code>muhendis</code> / <code>Muhendis!23</code>
    </div>
  </form>
</div>
</body>
</html>
