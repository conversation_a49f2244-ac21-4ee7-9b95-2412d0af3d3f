<?php
// Test date picker functionality
require __DIR__.'/../inc/auth.php';

require_login();
$me = current_user();
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Date Picker Test • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--panel:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;--line:#1f2937;}
    *{box-sizing:border-box} body{margin:0;background:#0b1220;color:var(--text);font:15px/1.55 system-ui,Segoe UI,Arial}
    header{display:flex;justify-content:space-between;align-items:center;padding:18px 22px;border-bottom:1px solid var(--line);background:#0f172a;position:sticky;top:0}
    main{max-width:800px;margin:18px auto;padding:0 22px}
    a{color:#93c5fd}
    .card{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:16px;margin:14px 0}
    h2{margin:0 0 12px;font-size:18px}
    label{display:block;margin:8px 0;color:#cbd5e1}
    input,select,textarea{width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px}
    textarea{min-height:90px}
    button{padding:10px 14px;border-radius:10px;border:0;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    table{width:100%;border-collapse:collapse;margin-top:12px}
    th,td{border-bottom:1px solid #1f2937;padding:10px;text-align:left}
    tr:hover td{background:#0b1725}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    .btns form{display:inline-block;margin-right:8px}
    .muted{color:#94a3b8}
    .instructions{background:#1e293b;padding:16px;border-radius:10px;margin:16px 0}
  </style>
</head>
<body>
<header>
  <div class="row">
    <a href="/requests.php">← Talepler</a>
  </div>
  <div>Date Picker Test</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <section class="card">
    <h2>Date Picker Test</h2>
    <p>Bu sayfa date picker fonksiyonelliğini test etmek için oluşturulmuştur.</p>
    
    <div class="instructions">
      <h3>Talimatlar:</h3>
      <ol>
        <li>Bir tarih alanından tarih seçin veya elle girin</li>
        <li>Ctrl+A ile tarihi seçin ve Ctrl+C ile kopyalayın</li>
        <li>Başka bir tarih alanına Ctrl+V ile yapıştırın</li>
        <li>Farklı formatlardaki tarihlerin otomatik olarak dönüştürüldüğünü gözlemleyin</li>
      </ol>
    </div>
    
    <form>
      <div class="row">
        <label style="flex:1 1 200px">Tarih 1 (YYYY-MM-DD)
          <input type="date" id="date1" value="<?=date('Y-m-d')?>">
        </label>
        <label style="flex:1 1 200px">Tarih 2 (MM/DD/YYYY formatında yapıştır)
          <input type="date" id="date2">
        </label>
      </div>
      
      <div class="row">
        <label style="flex:1 1 200px">Tarih 3 (DD/MM/YYYY formatında yapıştır)
          <input type="date" id="date3">
        </label>
        <label style="flex:1 1 200px">Tarih 4 (Herhangi bir formatta yapıştır)
          <input type="date" id="date4">
        </label>
      </div>
      
      <button type="button" onclick="showValues()">Değerleri Göster</button>
    </form>
    
    <div id="output" style="margin-top:20px;padding:10px;background:#1e293b;border-radius:8px;display:none;"></div>
  </section>
  
  <section class="card">
    <h2>Test Sonuçları</h2>
    <p>Tüm tarih alanları HTML5 date picker kullanmaktadır. Bu alanlar şu özellikleri sağlar:</p>
    <ul>
      <li>Görsel tarih seçici (takvim arayüzü)</li>
      <li>Elle tarih girişi (YYYY-MM-DD formatında)</li>
      <li>Copy/Paste desteği (Ctrl+C / Ctrl+V)</li>
      <li>Otomatik format dönüşümü (MM/DD/YYYY, DD/MM/YYYY → YYYY-MM-DD)</li>
    </ul>
  </section>
</main>

<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});

function showValues() {
    const date1 = document.getElementById('date1').value;
    const date2 = document.getElementById('date2').value;
    const date3 = document.getElementById('date3').value;
    const date4 = document.getElementById('date4').value;
    
    const output = document.getElementById('output');
    output.innerHTML = `
        <p><strong>Tarih 1:</strong> ${date1 || '(boş)'}</p>
        <p><strong>Tarih 2:</strong> ${date2 || '(boş)'}</p>
        <p><strong>Tarih 3:</strong> ${date3 || '(boş)'}</p>
        <p><strong>Tarih 4:</strong> ${date4 || '(boş)'}</p>
    `;
    output.style.display = 'block';
}
</script>
</body>
</html>