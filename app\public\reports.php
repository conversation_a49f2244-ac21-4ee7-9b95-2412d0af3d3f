<?php
// C:\workapp\app\public\reports.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
require_role(['Admin', 'Manager']);
$me = current_user();

// Varsayılan tarih a<PERSON>ığı: bu ay
$start_date = $_GET['start'] ?? date('Y-m-01');
$end_date = $_GET['end'] ?? date('Y-m-t');

// Filtre parametreleri
$unit_filter = $_GET['unit'] ?? '';
$engineer_filter = $_GET['engineer'] ?? '';
$status_filter = $_GET['status'] ?? '';

// Mühendisler
$engineers = $pdo->query("SELECT id, display_name FROM Users WHERE role_id=3 AND is_active=1 ORDER BY display_name")->fetchAll(PDO::FETCH_ASSOC);

// Birimler
$units = $pdo->query("SELECT DISTINCT unit FROM Requests WHERE unit IS NOT NULL AND unit != '' ORDER BY unit")->fetchAll(PDO::FETCH_ASSOC);

// Durumlar
$statuses = $pdo->query("SELECT id, label FROM Status ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);

// 1. Talep Bazlı Raporlar
// Toplam Talep Sayısı
$totalRequestsQuery = "SELECT COUNT(*) as total FROM Requests WHERE created_at BETWEEN :start AND :end";
$totalRequestsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $totalRequestsQuery .= " AND unit = :unit";
    $totalRequestsParams[':unit'] = $unit_filter;
}
$totalRequestsStmt = $pdo->prepare($totalRequestsQuery);
$totalRequestsStmt->execute($totalRequestsParams);
$totalRequests = $totalRequestsStmt->fetch(PDO::FETCH_ASSOC)['total'];

// Kapatılan talepler (DONE durumunda olanlar)
$closedRequestsQuery = "SELECT COUNT(*) as total FROM Requests WHERE status_id = 6 AND created_at BETWEEN :start AND :end";
$closedRequestsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $closedRequestsQuery .= " AND unit = :unit";
    $closedRequestsParams[':unit'] = $unit_filter;
}
$closedRequestsStmt = $pdo->prepare($closedRequestsQuery);
$closedRequestsStmt->execute($closedRequestsParams);
$closedRequests = $closedRequestsStmt->fetch(PDO::FETCH_ASSOC)['total'];

// Durum Dağılımı
$statusDistributionQuery = "
    SELECT s.label, COUNT(*) as count
    FROM Requests r
    JOIN Status s ON r.status_id = s.id
    WHERE r.created_at BETWEEN :start AND :end
";
$params3 = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $statusDistributionQuery .= " AND r.unit = :unit";
    $params3[':unit'] = $unit_filter;
}
$statusDistributionQuery .= " GROUP BY r.status_id ORDER BY count DESC";
$statusDistributionStmt = $pdo->prepare($statusDistributionQuery);
$statusDistributionStmt->execute($params3);
$statusDistribution = $statusDistributionStmt->fetchAll(PDO::FETCH_ASSOC);

// Malzemelere Göre Talepler (Yeni eklenen bölüm)
$materialRequestsQuery = "
    SELECT m.name, m.code, COUNT(DISTINCT r.id) as request_count, SUM(mu.quantity) as total_quantity, mu.unit
    FROM MaterialUsage mu
    JOIN Materials m ON m.id = mu.material_id
    JOIN Requests r ON r.id = mu.request_id
    WHERE r.created_at BETWEEN :start AND :end
";
$materialRequestsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $materialRequestsQuery .= " AND r.unit = :unit";
    $materialRequestsParams[':unit'] = $unit_filter;
}
$materialRequestsQuery .= " GROUP BY m.id, m.name, m.code, mu.unit ORDER BY total_quantity DESC LIMIT 10";
$materialRequestsStmt = $pdo->prepare($materialRequestsQuery);
$materialRequestsStmt->execute($materialRequestsParams);
$materialRequests = $materialRequestsStmt->fetchAll(PDO::FETCH_ASSOC);

// Önceliğe Göre Talepler
$priorityDistributionQuery = "
    SELECT p.name, COUNT(*) as count
    FROM Requests r
    JOIN Priorities p ON r.priority_id = p.id
    WHERE r.created_at BETWEEN :start AND :end
";
$priorityDistributionParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $priorityDistributionQuery .= " AND r.unit = :unit";
    $priorityDistributionParams[':unit'] = $unit_filter;
}
$priorityDistributionQuery .= " GROUP BY r.priority_id ORDER BY count DESC";
$priorityDistributionStmt = $pdo->prepare($priorityDistributionQuery);
$priorityDistributionStmt->execute($priorityDistributionParams);
$priorityDistribution = $priorityDistributionStmt->fetchAll(PDO::FETCH_ASSOC);

// 2. Performans & SLA Raporları
// Talep Yaşam Süresi (Ortalama kapanış süresi)
$avgLifeTimeQuery = "
    SELECT AVG(JULIANDAY(actual_finish) - JULIANDAY(created_at)) as avg_days
    FROM Requests
    WHERE status_id = 6 AND actual_finish IS NOT NULL AND created_at BETWEEN :start AND :end
";
$avgLifeTimeParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $avgLifeTimeQuery .= " AND unit = :unit";
    $avgLifeTimeParams[':unit'] = $unit_filter;
}
$avgLifeTimeStmt = $pdo->prepare($avgLifeTimeQuery);
$avgLifeTimeStmt->execute($avgLifeTimeParams);
$avgLifeTime = $avgLifeTimeStmt->fetch(PDO::FETCH_ASSOC)['avg_days'];

// Onay Süresi (Yönetici onayına gönderilen taleplerin ortalama onay süresi)
$avgApprovalTimeQuery = "
    SELECT AVG(JULIANDAY(approved_at) - JULIANDAY(created_at)) as avg_days
    FROM Requests
    WHERE status_id IN (4, 6) AND approved_at IS NOT NULL AND created_at BETWEEN :start AND :end
";
$avgApprovalTimeParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $avgApprovalTimeQuery .= " AND unit = :unit";
    $avgApprovalTimeParams[':unit'] = $unit_filter;
}
$avgApprovalTimeStmt = $pdo->prepare($avgApprovalTimeQuery);
$avgApprovalTimeStmt->execute($avgApprovalTimeParams);
$avgApprovalTime = $avgApprovalTimeStmt->fetch(PDO::FETCH_ASSOC)['avg_days'];

// Mühendis Performansı (Her mühendisin kapattığı talep sayısı ve ortalama çözüm süresi)
$engineerPerformanceQuery = "
    SELECT u.display_name, 
           COUNT(r.id) as closed_requests,
           AVG(JULIANDAY(r.actual_finish) - JULIANDAY(r.created_at)) as avg_resolution_days
    FROM Requests r
    JOIN Users u ON r.assigned_engineer = u.id
    WHERE r.status_id = 6 AND r.actual_finish IS NOT NULL AND r.created_at BETWEEN :start AND :end
";
$engineerPerformanceParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $engineerPerformanceQuery .= " AND r.unit = :unit";
    $engineerPerformanceParams[':unit'] = $unit_filter;
}
$engineerPerformanceQuery .= " GROUP BY u.id, u.display_name ORDER BY closed_requests DESC";
$engineerPerformanceStmt = $pdo->prepare($engineerPerformanceQuery);
$engineerPerformanceStmt->execute($engineerPerformanceParams);
$engineerPerformance = $engineerPerformanceStmt->fetchAll(PDO::FETCH_ASSOC);

// 3. Adam/Saat Raporları
// Toplam İşçilik (adam/saat)
$totalWorkHoursQuery = "
    SELECT SUM(hours) as total_hours
    FROM WorkerAssignments
    WHERE date BETWEEN :start AND :end
";
$totalWorkHoursParams = [':start' => $start_date, ':end' => $end_date];
$totalWorkHoursStmt = $pdo->prepare($totalWorkHoursQuery);
$totalWorkHoursStmt->execute($totalWorkHoursParams);
$totalWorkHours = $totalWorkHoursStmt->fetch(PDO::FETCH_ASSOC)['total_hours'] ?? 0;

// Çalışana Göre İşçilik
$workerHoursQuery = "
    SELECT w.full_name, SUM(a.hours) AS total_hours
    FROM WorkerAssignments a
    JOIN Workers w ON w.id=a.worker_id
    WHERE a.date BETWEEN :start AND :end
";
$workerHoursParams = [':start' => $start_date, ':end' => $end_date];
if ($unit_filter) {
    $workerHoursQuery .= " AND EXISTS (SELECT 1 FROM Requests r WHERE r.id = a.request_id AND r.unit = :unit)";
    $workerHoursParams[':unit'] = $unit_filter;
}
$workerHoursQuery .= " GROUP BY w.id, w.full_name ORDER BY total_hours DESC";
$workerHoursStmt = $pdo->prepare($workerHoursQuery);
$workerHoursStmt->execute($workerHoursParams);
$workerHours = $workerHoursStmt->fetchAll(PDO::FETCH_ASSOC);

// 4. Malzeme Kullanım Raporları
// Malzeme Koduna Göre Tüketim
$materialUsageByCodeQuery = "
    SELECT m.code, m.name, SUM(u.quantity) AS total_qty, m.unit
    FROM MaterialUsage u
    JOIN Materials m ON m.id = u.material_id
    JOIN Requests r ON r.id = u.request_id
    WHERE r.created_at BETWEEN :start AND :end
";
$materialUsageByCodeParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $materialUsageByCodeQuery .= " AND r.unit = :unit";
    $materialUsageByCodeParams[':unit'] = $unit_filter;
}
$materialUsageByCodeQuery .= " GROUP BY m.id, m.code, m.name, m.unit ORDER BY total_qty DESC";
$materialUsageByCodeStmt = $pdo->prepare($materialUsageByCodeQuery);
$materialUsageByCodeStmt->execute($materialUsageByCodeParams);
$materialUsageByCode = $materialUsageByCodeStmt->fetchAll(PDO::FETCH_ASSOC);

// Talep Bazlı Malzeme Kullanımı
$requestMaterialUsageQuery = "
    SELECT r.id, r.title, m.name, u.quantity, u.unit
    FROM MaterialUsage u
    JOIN Materials m ON m.id = u.material_id
    JOIN Requests r ON r.id = u.request_id
    WHERE r.created_at BETWEEN :start AND :end
";
$requestMaterialUsageParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $requestMaterialUsageQuery .= " AND r.unit = :unit";
    $requestMaterialUsageParams[':unit'] = $unit_filter;
}
$requestMaterialUsageQuery .= " ORDER BY r.id, m.name";
$requestMaterialUsageStmt = $pdo->prepare($requestMaterialUsageQuery);
$requestMaterialUsageStmt->execute($requestMaterialUsageParams);
$requestMaterialUsage = $requestMaterialUsageStmt->fetchAll(PDO::FETCH_ASSOC);

// Birim Bazlı Malzeme Tüketimi
$unitMaterialUsageQuery = "
    SELECT r.unit, m.name, SUM(u.quantity) AS total_qty
    FROM MaterialUsage u
    JOIN Materials m ON m.id = u.material_id
    JOIN Requests r ON r.id = u.request_id
    WHERE r.created_at BETWEEN :start AND :end AND r.unit IS NOT NULL
";
$unitMaterialUsageParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $unitMaterialUsageQuery .= " AND r.unit = :unit";
    $unitMaterialUsageParams[':unit'] = $unit_filter;
}
$unitMaterialUsageQuery .= " GROUP BY r.unit, m.name ORDER BY r.unit, total_qty DESC";
$unitMaterialUsageStmt = $pdo->prepare($unitMaterialUsageQuery);
$unitMaterialUsageStmt->execute($unitMaterialUsageParams);
$unitMaterialUsage = $unitMaterialUsageStmt->fetchAll(PDO::FETCH_ASSOC);

// 5. Yönetici Onayı Raporları
// Bekleyen Onaylar (PENDING_MANAGER durumundaki talepler)
$pendingApprovalsQuery = "
    SELECT COUNT(*) as total
    FROM Requests
    WHERE status_id = 3 AND created_at BETWEEN :start AND :end
";
$pendingApprovalsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $pendingApprovalsQuery .= " AND unit = :unit";
    $pendingApprovalsParams[':unit'] = $unit_filter;
}
$pendingApprovalsStmt = $pdo->prepare($pendingApprovalsQuery);
$pendingApprovalsStmt->execute($pendingApprovalsParams);
$pendingApprovals = $pendingApprovalsStmt->fetch(PDO::FETCH_ASSOC)['total'];

// Reddedilen Talepler
$rejectedRequestsQuery = "
    SELECT COUNT(*) as total
    FROM Requests
    WHERE status_id = 7 AND created_at BETWEEN :start AND :end
";
$rejectedRequestsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $rejectedRequestsQuery .= " AND unit = :unit";
    $rejectedRequestsParams[':unit'] = $unit_filter;
}
$rejectedRequestsStmt = $pdo->prepare($rejectedRequestsQuery);
$rejectedRequestsStmt->execute($rejectedRequestsParams);
$rejectedRequests = $rejectedRequestsStmt->fetch(PDO::FETCH_ASSOC)['total'];

// Red Sebepleri Analizi (StatusLog tablosundan reddedilme notlarını al)
$rejectionReasonsQuery = "
    SELECT note, COUNT(*) as count
    FROM StatusLog
    WHERE new_status_id = 7 AND changed_at BETWEEN :start AND :end
    AND note LIKE 'Yönetici reddetti:%'
";
$rejectionReasonsParams = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];
if ($unit_filter) {
    $rejectionReasonsQuery .= " AND EXISTS (SELECT 1 FROM Requests r WHERE r.id = request_id AND r.unit = :unit)";
    $rejectionReasonsParams[':unit'] = $unit_filter;
}
$rejectionReasonsQuery .= " GROUP BY note ORDER BY count DESC";
$rejectionReasonsStmt = $pdo->prepare($rejectionReasonsQuery);
$rejectionReasonsStmt->execute($rejectionReasonsParams);
$rejectionReasons = $rejectionReasonsStmt->fetchAll(PDO::FETCH_ASSOC);

// Onaylanan vs. Reddedilen Oranları
$totalProcessedRequests = $rejectedRequests + $closedRequests; // Approximation
$approvalRate = $totalProcessedRequests > 0 ? ($closedRequests / $totalProcessedRequests) * 100 : 0;
$rejectionRate = $totalProcessedRequests > 0 ? ($rejectedRequests / $totalProcessedRequests) * 100 : 0;

// Dashboard verileri
// Toplam aktif personel sayısı
$totalWorkers = $pdo->query("SELECT COUNT(*) as count FROM Workers WHERE is_active = 1")->fetch(PDO::FETCH_ASSOC)['count'];

// Haftalık toplam saat
$weeklyHoursQuery = "
    SELECT SUM(hours) as total_hours
    FROM WorkerAssignments
    WHERE date BETWEEN :start AND :end
";
$weeklyHoursStmt = $pdo->prepare($weeklyHoursQuery);
$weeklyHoursStmt->execute([':start' => $start_date, ':end' => $end_date]);
$weeklyHours = $weeklyHoursStmt->fetch(PDO::FETCH_ASSOC)['total_hours'] ?? 0;

// Oran: Haftalık saat / toplam personel
$hoursToWorkersRatio = $totalWorkers > 0 ? $weeklyHours / $totalWorkers : 0;

// Toplam talep sayısı (zaten hesapladık)
// $totalRequests

// Acil taleplerin oranı
$urgentRequests = 0;
$totalPriorityRequests = 0;
foreach ($priorityDistribution as $priority) {
    $totalPriorityRequests += $priority['count'];
    if ($priority['name'] === 'Urgent') {
        $urgentRequests = $priority['count'];
    }
}
$urgentRate = $totalPriorityRequests > 0 ? ($urgentRequests / $totalPriorityRequests) * 100 : 0;
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Raporlar • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--panel:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;--line:#1f2937;}
    *{box-sizing:border-box} body{margin:0;background:#0b1220;color:var(--text);font:15px/1.55 system-ui,Segoe UI,Arial}
    header{display:flex;justify-content:space-between;align-items:center;padding:18px 22px;border-bottom:1px solid var(--line);background:#0f172a;position:sticky;top:0}
    main{max-width:1200px;margin:18px auto;padding:0 22px}
    a{color:#93c5fd}
    .card{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:16px;margin:14px 0}
    h2{margin:0 0 12px;font-size:18px}
    label{display:block;margin:8px 0;color:#cbd5e1}
    input,select{width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px}
    button{padding:10px 14px;border-radius:10px;border:0;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    table{width:100%;border-collapse:collapse;margin-top:12px}
    th,td{border-bottom:1px solid #1f2937;padding:10px;text-align:left}
    tr:hover td{background:#0b1725}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    .muted{color:#94a3b8}
    .filters{display:flex;gap:12px;flex-wrap:wrap;align-items:end}
    .filter-item{flex:1 1 200px}
    .actions{text-align:right;margin:16px 0}
    
    /* Dashboard styles */
    .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
    .metric-card { background: #1e293b; border-radius: 10px; padding: 20px; text-align: center; }
    .metric-value { font-size: 2em; font-weight: bold; color: #22c55e; margin: 10px 0; }
    .metric-label { color: #94a3b8; }
    .chart-container { height: 300px; margin: 20px 0; }
    
    /* Report sections */
    .report-section { margin-bottom: 30px; }
    .report-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    .report-card { background: #1e293b; border-radius: 10px; padding: 15px; }
    .report-title { font-size: 1.2em; font-weight: bold; margin-bottom: 10px; color: #93c5fd; }
    .report-value { font-size: 1.5em; font-weight: bold; color: #22c55e; }
    .report-subtitle { font-size: 1em; color: #cbd5e1; margin: 5px 0; }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<header>
  <div class="row">
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
    <a href="/requests.php">Talepler</a>
  </div>
  <div>Raporlar</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <!-- Dashboard Section -->
  <section class="card">
    <div class="row" style="justify-content:space-between;align-items:center">
      <h2>Raporlar</h2>
      <div>
        <a href="/export_requests.php" style="display:inline-block;padding:8px 12px;background:#0ea5e9;color:white;border-radius:8px;text-decoration:none;margin-right:10px">
          Tüm Talepleri Dışa Aktar (CSV)
        </a>
        <a href="/export_dashboard.php?start=<?=e($start_date)?>&end=<?=e($end_date)?>" style="display:inline-block;padding:8px 12px;background:#8b5cf6;color:white;border-radius:8px;text-decoration:none">
          Dashboard Verilerini Dışa Aktar (CSV)
        </a>
      </div>
    </div>
    
    <div class="row">
      <div class="metric-card">
        <div class="metric-label">Toplam Talep</div>
        <div class="metric-value"><?= $totalRequests ?></div>
        <div class="muted">Bu dönem açılan</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Kapatılan Talep</div>
        <div class="metric-value"><?= $closedRequests ?></div>
        <div class="muted">Tamamlanan işler</div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Toplam Adam/Saat</div>
        <div class="metric-value"><?= number_format((float)$totalWorkHours, 2, ',', '.') ?></div>
        <div class="muted">Bu dönem harcanan</div>
      </div>
    </div>
  </section>

  <section class="card">
    <h2>Filtreler</h2>
    <form method="get" class="filters">
      <div class="filter-item">
        <label>Başlangıç Tarihi
          <input type="date" name="start" value="<?=e($start_date)?>">
        </label>
      </div>
      <div class="filter-item">
        <label>Bitiş Tarihi
          <input type="date" name="end" value="<?=e($end_date)?>">
        </label>
      </div>
      <div class="filter-item">
        <label>Birim
          <select name="unit">
            <option value="">Tümü</option>
            <?php foreach($units as $u): ?>
              <option value="<?=e($u['unit'])?>" <?=($unit_filter==$u['unit'])?'selected':''?>><?=e($u['unit'])?></option>
            <?php endforeach; ?>
          </select>
        </label>
      </div>
      <div class="filter-item">
        <label>Mühendis
          <select name="engineer">
            <option value="">Tümü</option>
            <?php foreach($engineers as $e): ?>
              <option value="<?=$e['id']?>" <?=($engineer_filter==$e['id'])?'selected':''?>><?=e($e['display_name'])?></option>
            <?php endforeach; ?>
          </select>
        </label>
      </div>
      <div class="filter-item">
        <label>Durum
          <select name="status">
            <option value="">Tümü</option>
            <?php foreach($statuses as $s): ?>
              <option value="<?=$s['id']?>" <?=($status_filter==$s['id'])?'selected':''?>><?=e($s['label'])?></option>
            <?php endforeach; ?>
          </select>
        </label>
      </div>
      <div class="filter-item">
        <button type="submit">Filtrele</button>
      </div>
    </form>
  </section>

  <!-- 1. Talep Bazlı Raporlar -->
  <section class="card report-section">
    <h2>1. Talep Bazlı Raporlar</h2>
    <div class="report-grid">
      <div class="report-card">
        <div class="report-title">Durum Dağılımı</div>
        <table>
          <thead>
            <tr><th>Durum</th><th>Sayı</th></tr>
          </thead>
          <tbody>
            <?php foreach($statusDistribution as $status): ?>
            <tr>
              <td><?=e($status['label'])?></td>
              <td><?=e($status['count'])?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
      
      <div class="report-card">
        <div class="report-title">Malzemelere Göre Talepler</div>
        <table>
          <thead>
            <tr><th>Kod</th><th>Malzeme</th><th>Talep Sayısı</th><th>Toplam Miktar</th></tr>
          </thead>
          <tbody>
            <?php foreach($materialRequests as $material): ?>
            <tr>
              <td><?=e($material['code'] ?? '-')?></td>
              <td><?=e($material['name'])?></td>
              <td><?=e($material['request_count'])?></td>
              <td><?=number_format((float)$material['total_quantity'], 2, ',', '.')?> <?=e($material['unit'])?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
      
      <div class="report-card">
        <div class="report-title">Önceliğe Göre Talepler</div>
        <table>
          <thead>
            <tr><th>Öncelik</th><th>Sayı</th></tr>
          </thead>
          <tbody>
            <?php foreach($priorityDistribution as $priority): ?>
            <tr>
              <td><?=e($priority['name'])?></td>
              <td><?=e($priority['count'])?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
      
      <div class="report-card">
        <div class="report-title">Talep İstatistikleri</div>
        <div class="report-subtitle">Toplam Talep</div>
        <div class="report-value"><?= $totalRequests ?></div>
        <div class="report-subtitle">Kapatılan Talep</div>
        <div class="report-value"><?= $closedRequests ?></div>
        <div class="report-subtitle">Acil Talep Oranı</div>
        <div class="report-value"><?= number_format((float)$urgentRate, 1, ',', '.') ?>%</div>
      </div>
    </div>
  </section>

  <!-- 2. Performans & SLA Raporları -->
  <section class="card report-section">
    <h2>2. Performans & SLA Raporları</h2>
    <div class="report-grid">
      <div class="report-card">
        <div class="report-title">Talep Yaşam Süresi</div>
        <div class="report-subtitle">Ortalama Kapanış Süresi</div>
        <div class="report-value"><?= $avgLifeTime ? number_format((float)$avgLifeTime, 1, ',', '.') . ' gün' : 'Veri yok' ?></div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Onay Süresi</div>
        <div class="report-subtitle">Ortalama Onay Süresi</div>
        <div class="report-value"><?= $avgApprovalTime ? number_format((float)$avgApprovalTime, 1, ',', '.') . ' gün' : 'Veri yok' ?></div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Mühendis Performansı</div>
        <table>
          <thead>
            <tr><th>Mühendis</th><th>Kapattığı</th><th>Ort. Süre</th></tr>
          </thead>
          <tbody>
            <?php foreach($engineerPerformance as $eng): ?>
            <tr>
              <td><?=e($eng['display_name'])?></td>
              <td><?=e($eng['closed_requests'])?></td>
              <td><?= $eng['avg_resolution_days'] ? number_format((float)$eng['avg_resolution_days'], 1, ',', '.') . ' gün' : 'Veri yok' ?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <!-- 3. Adam/Saat Raporları -->
  <section class="card report-section">
    <h2>3. Adam/Saat Raporları</h2>
    <div class="report-grid">
      <div class="report-card">
        <div class="report-title">Toplam İşçilik</div>
        <div class="report-value"><?= number_format((float)$totalWorkHours, 2, ',', '.') ?> saat</div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Çalışana Göre Dağılım</div>
        <table>
          <thead>
            <tr><th>Çalışan</th><th>Saat</th></tr>
          </thead>
          <tbody>
            <?php foreach($workerHours as $worker): ?>
            <tr>
              <td><?=e($worker['full_name'])?></td>
              <td><?=number_format((float)$worker['total_hours'], 2, ',', '.')?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <!-- 4. Malzeme Kullanım Raporları -->
  <section class="card report-section">
    <h2>4. Malzeme Kullanım Raporları</h2>
    <div class="report-grid">
      <div class="report-card">
        <div class="report-title">En Çok Kullanılan Malzemeler</div>
        <table>
          <thead>
            <tr><th>Kod</th><th>Malzeme</th><th>Miktar</th></tr>
          </thead>
          <tbody>
            <?php foreach($materialUsageByCode as $material): ?>
            <tr>
              <td><?=e($material['code'] ?? '-')?></td>
              <td><?=e($material['name'])?></td>
              <td><?=number_format((float)$material['total_qty'], 2, ',', '.')?> <?=e($material['unit'])?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <!-- 5. Yönetici Onayı Raporları -->
  <section class="card report-section">
    <h2>5. Yönetici Onayı Raporları</h2>
    <div class="report-grid">
      <div class="report-card">
        <div class="report-title">Onay Bekleyen Talepler</div>
        <div class="report-value"><?= $pendingApprovals ?></div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Reddedilen Talepler</div>
        <div class="report-value"><?= $rejectedRequests ?></div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Onay/Red Oranları</div>
        <div class="report-subtitle">Onay Oranı</div>
        <div class="report-value"><?= number_format((float)$approvalRate, 1, ',', '.') ?>%</div>
        <div class="report-subtitle">Reddetme Oranı</div>
        <div class="report-value"><?= number_format((float)$rejectionRate, 1, ',', '.') ?>%</div>
      </div>
      
      <div class="report-card">
        <div class="report-title">Red Sebepleri</div>
        <table>
          <thead>
            <tr><th>Sebep</th><th>Sayı</th></tr>
          </thead>
          <tbody>
            <?php foreach($rejectionReasons as $reason): ?>
            <tr>
              <td><?=e(substr($reason['note'], strpos($reason['note'], ':') + 2))?></td>
              <td><?=e($reason['count'])?></td>
            </tr>
            <?php endforeach; ?>
          </tbody>
        </table>
      </div>
    </div>
  </section>
</main>
<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});

// Add double-click sorting to all table headers
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            // Add sort indicator
            const indicator = document.createElement('span');
            indicator.className = 'sort-indicator';
            indicator.style.marginLeft = '5px';
            header.appendChild(indicator);
            
            // Determine data type (simple heuristic)
            const firstRow = table.querySelector('tbody tr');
            let dataType = 'text';
            if (firstRow) {
                const cellText = firstRow.cells[index].textContent.trim();
                if (!isNaN(cellText) && cellText !== '') {
                    dataType = 'number';
                }
            }
            
            // Add double-click event
            header.addEventListener('dblclick', () => {
                sortTable(table, index, dataType);
            });
        });
    });
});

function sortTable(table, columnIndex, dataType) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.getAttribute('data-sort-order') !== 'asc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        let comparison = 0;
        
        if (dataType === 'number') {
            const aNum = parseFloat(aText) || 0;
            const bNum = parseFloat(bText) || 0;
            comparison = aNum - bNum;
        } else {
            comparison = aText.localeCompare(bText, 'tr', { numeric: true, sensitivity: 'base' });
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Toggle sort order
    table.setAttribute('data-sort-order', isAscending ? 'asc' : 'desc');
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update header indicators
    const headers = table.querySelectorAll('th');
    headers.forEach((header, index) => {
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
            if (index === columnIndex) {
                indicator.textContent = isAscending ? ' ↑' : ' ↓';
            } else {
                indicator.textContent = '';
            }
        }
    });
}
</script>
</body>
</html>