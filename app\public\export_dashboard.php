<?php
// Export dashboard data to CSV
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

// Only allow Ad<PERSON> and Manager roles to export
if (!in_array($me['role'], ['Admin', 'Manager'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Get date range from query parameters or use defaults
$start_date = $_GET['start'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end'] ?? date('Y-m-d');

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=dashboard_report_' . date('Y-m-d_H-i-s') . '.csv');

// Open output stream
$output = fopen('php://output', 'w');

// Add BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Export section 1: Requests by Status
fputcsv($output, ['Talep Durumlarına Göre Dağılım']);
fputcsv($output, ['Durum', 'Adet']);

$stmt = $pdo->prepare("SELECT s.label, COUNT(*) as count 
                      FROM Requests r 
                      JOIN Status s ON r.status_id = s.id 
                      WHERE DATE(r.created_at) BETWEEN ? AND ? 
                      GROUP BY s.label 
                      ORDER BY count DESC");
$stmt->execute([$start_date, $end_date]);
$status_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($status_data as $row) {
    fputcsv($output, [$row['label'], $row['count']]);
}

fputcsv($output, []); // Empty row

// Export section 2: Requests by Priority
fputcsv($output, ['Önceliklere Göre Talepler']);
fputcsv($output, ['Öncelik', 'Adet']);

$stmt = $pdo->prepare("SELECT p.name, COUNT(*) as count 
                      FROM Requests r 
                      JOIN Priorities p ON r.priority_id = p.id 
                      WHERE DATE(r.created_at) BETWEEN ? AND ? 
                      GROUP BY p.name 
                      ORDER BY count DESC");
$stmt->execute([$start_date, $end_date]);
$priority_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($priority_data as $row) {
    fputcsv($output, [$row['name'], $row['count']]);
}

fputcsv($output, []); // Empty row

// Export section 3: Requests by Unit
fputcsv($output, ['Birimlere Göre Talepler']);
fputcsv($output, ['Birim', 'Adet']);

$stmt = $pdo->prepare("SELECT unit, COUNT(*) as count 
                      FROM Requests 
                      WHERE DATE(created_at) BETWEEN ? AND ? AND unit IS NOT NULL AND unit != ''
                      GROUP BY unit 
                      ORDER BY count DESC");
$stmt->execute([$start_date, $end_date]);
$unit_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($unit_data as $row) {
    fputcsv($output, [$row['unit'], $row['count']]);
}

fputcsv($output, []); // Empty row

// Export section 4: Materials Usage
fputcsv($output, ['En Çok Kullanılan Malzemeler']);
fputcsv($output, ['Malzeme', 'Kod', 'Toplam Miktar', 'Birim']);

$stmt = $pdo->prepare("SELECT m.name, m.code, SUM(mu.quantity) as total_quantity, mu.unit
                      FROM MaterialUsage mu
                      JOIN Materials m ON mu.material_id = m.id
                      JOIN Requests r ON mu.request_id = r.id
                      WHERE DATE(r.created_at) BETWEEN ? AND ?
                      GROUP BY m.name, m.code, mu.unit
                      ORDER BY total_quantity DESC
                      LIMIT 20");
$stmt->execute([$start_date, $end_date]);
$material_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($material_data as $row) {
    fputcsv($output, [$row['name'], $row['code'], $row['total_quantity'], $row['unit']]);
}

fputcsv($output, []); // Empty row

// Export section 5: Worker Hours
fputcsv($output, ['En Çok Çalışma Saati Olan İşçiler']);
fputcsv($output, ['İşçi', 'Meslek', 'Toplam Saat']);

$stmt = $pdo->prepare("SELECT w.full_name, w.trade, SUM(wa.hours) as total_hours
                      FROM WorkerAssignments wa
                      JOIN Workers w ON wa.worker_id = w.id
                      JOIN Requests r ON wa.request_id = r.id
                      WHERE DATE(r.created_at) BETWEEN ? AND ?
                      GROUP BY w.full_name, w.trade
                      ORDER BY total_hours DESC
                      LIMIT 20");
$stmt->execute([$start_date, $end_date]);
$worker_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($worker_data as $row) {
    fputcsv($output, [$row['full_name'], $row['trade'], $row['total_hours']]);
}

fclose($output);
exit;
?>