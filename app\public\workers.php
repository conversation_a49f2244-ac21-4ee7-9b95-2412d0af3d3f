<?php
// C:\workapp\app\public\workers.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
// Only Managers and <PERSON>mins can manage workers
require_role(['Admin', 'Manager']);
$me = current_user();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new worker
    if (isset($_POST['add_worker'])) {
        $full_name = trim($_POST['full_name'] ?? '');
        $trade = trim($_POST['trade'] ?? '');
        $sicil_no = trim($_POST['sicil_no'] ?? '');
        
        if ($full_name !== '') {
            $stmt = $pdo->prepare("INSERT INTO Workers(full_name, trade, sicil_no, is_active) VALUES(?, ?, ?, 1)");
            $stmt->execute([$full_name, $trade, $sicil_no]);
        }
        redirect('/workers.php');
    }
    
    // Update worker
    if (isset($_POST['update_worker'])) {
        $id = (int)($_POST['id'] ?? 0);
        $full_name = trim($_POST['full_name'] ?? '');
        $trade = trim($_POST['trade'] ?? '');
        $sicil_no = trim($_POST['sicil_no'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if ($id > 0 && $full_name !== '') {
            $stmt = $pdo->prepare("UPDATE Workers SET full_name=?, trade=?, sicil_no=?, is_active=? WHERE id=?");
            $stmt->execute([$full_name, $trade, $sicil_no, $is_active, $id]);
        }
        redirect('/workers.php');
    }
    
    // Delete worker (soft delete)
    if (isset($_POST['delete_worker'])) {
        $id = (int)($_POST['id'] ?? 0);
        if ($id > 0) {
            // Instead of deleting, we'll just mark as inactive
            $stmt = $pdo->prepare("UPDATE Workers SET is_active=0 WHERE id=?");
            $stmt->execute([$id]);
        }
        redirect('/workers.php');
    }
}

// Get all workers
$workers = $pdo->query("SELECT * FROM Workers ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);

// Get trades for dropdown
$trades = $pdo->query("SELECT DISTINCT trade FROM Workers WHERE trade IS NOT NULL AND trade != '' ORDER BY trade")->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Çalışanlar • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="/assets/css/workapp-modern.css">
  <style>
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 1000;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: var(--space-xl);
      max-width: 500px;
      width: 90%;
      box-shadow: var(--shadow-xl);
    }

    .close {
      float: right;
      font-size: var(--font-size-xl);
      cursor: pointer;
      color: var(--text-muted);
      transition: var(--transition-base);
    }

    .close:hover {
      color: var(--accent-danger);
    }

    .status-active {
      color: var(--accent-success);
    }

    .status-inactive {
      color: var(--accent-danger);
    }
  </style>
</head>
<body>
<header>
  <div class="row">
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
    <a href="/requests.php">Talepler</a>
    <a href="/workers.php">Çalışanlar</a>
    <a href="/materials.php">Malzemeler</a>
  </div>
  <div>Çalışanlar</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <section class="card">
    <h2>Yeni Çalışan Ekle</h2>
    <form method="post" class="row">
      <input type="hidden" name="add_worker" value="1">
      <label style="flex:1 1 150px">Sicil No
        <input name="sicil_no" placeholder="Sicil numarası...">
      </label>
      <label style="flex:2 1 300px">Ad Soyad
        <input name="full_name" placeholder="Çalışanın adı soyadı..." required>
      </label>
      <label style="flex:1 1 150px">Meslek
        <input name="trade" placeholder="Elektrik, Mekanik, vs...">
      </label>
      <button style="align-self:flex-end">Ekle</button>
    </form>
  </section>

  <section class="card">
    <h2>Çalışan Listesi</h2>
    <?php if($workers): ?>
    <table>
      <tr>
        <th>Sicil No</th>
        <th>Ad Soyad</th>
        <th>Meslek</th>
        <th>Durum</th>
        <th>İşlem</th>
      </tr>
      <?php foreach($workers as $worker): ?>
      <tr>
        <td><?=e($worker['sicil_no'] ?? '-')?></td>
        <td><?=e($worker['full_name'])?></td>
        <td><?=e($worker['trade'] ?? '-')?></td>
        <td>
          <?php if($worker['is_active']): ?>
            <span class="status-active">Aktif</span>
          <?php else: ?>
            <span class="status-inactive">Pasif</span>
          <?php endif; ?>
        </td>
        <td>
          <button onclick="editWorker(<?=$worker['id']?>, '<?=e($worker['sicil_no'] ?? '')?>', '<?=e($worker['full_name'])?>', '<?=e($worker['trade'] ?? '')?>', <?=$worker['is_active']?>)">Düzenle</button>
          <?php if($worker['is_active']): ?>
          <form method="post" style="display:inline-block">
            <input type="hidden" name="id" value="<?=$worker['id']?>">
            <input type="hidden" name="delete_worker" value="1">
            <button type="submit" onclick="return confirm('Çalışanı pasif yapmak istediğinizden emin misiniz?')">Pasif Yap</button>
          </form>
          <?php endif; ?>
        </td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <p class="muted">Kayıtlı çalışan bulunamadı.</p>
    <?php endif; ?>
  </section>
</main>

<!-- Edit Worker Modal -->
<div id="editModal" class="modal">
  <div class="modal-content">
    <span class="close" onclick="closeModal()">&times;</span>
    <h2>Çalışan Düzenle</h2>
    <form method="post" id="editForm">
      <input type="hidden" name="id" id="editId">
      <input type="hidden" name="update_worker" value="1">
      <label>Sicil No
        <input name="sicil_no" id="editSicilNo" placeholder="Sicil numarası...">
      </label>
      <label>Ad Soyad
        <input name="full_name" id="editFullName" placeholder="Çalışanın adı soyadı..." required>
      </label>
      <label>Meslek
        <input name="trade" id="editTrade" placeholder="Elektrik, Mekanik, vs...">
      </label>
      <label>
        <input type="checkbox" name="is_active" id="editIsActive" value="1"> Aktif
      </label>
      <button type="submit">Güncelle</button>
    </form>
  </div>
</div>

<script>
function editWorker(id, sicilNo, fullName, trade, isActive) {
    document.getElementById('editId').value = id;
    document.getElementById('editSicilNo').value = sicilNo;
    document.getElementById('editFullName').value = fullName;
    document.getElementById('editTrade').value = trade;
    document.getElementById('editIsActive').checked = isActive == 1;
    document.getElementById('editModal').style.display = 'flex';
}

function closeModal() {
    document.getElementById('editModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('editModal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

// Sorting functionality
function sortTable(table, columnIndex, dataType) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.getAttribute('data-sort-order') !== 'asc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        let comparison = 0;
        
        if (dataType === 'number') {
            const aNum = parseFloat(aText) || 0;
            const bNum = parseFloat(bText) || 0;
            comparison = aNum - bNum;
        } else {
            comparison = aText.localeCompare(bText, 'tr', { numeric: true, sensitivity: 'base' });
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Toggle sort order
    table.setAttribute('data-sort-order', isAscending ? 'asc' : 'desc');
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update header indicators
    const headers = table.querySelectorAll('th');
    headers.forEach((header, index) => {
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
            if (index === columnIndex) {
                indicator.textContent = isAscending ? ' ↑' : ' ↓';
            } else {
                indicator.textContent = '';
            }
        }
    });
}

// Add double-click sorting to all table headers
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            // Add sort indicator
            const indicator = document.createElement('span');
            indicator.className = 'sort-indicator';
            indicator.style.marginLeft = '5px';
            header.appendChild(indicator);
            
            // Determine data type (simple heuristic)
            const firstRow = table.querySelector('tbody tr');
            let dataType = 'text';
            if (firstRow) {
                const cellText = firstRow.cells[index].textContent.trim();
                if (!isNaN(cellText) && cellText !== '') {
                    dataType = 'number';
                }
            }
            
            // Add double-click event
            header.addEventListener('dblclick', () => {
                sortTable(table, index, dataType);
            });
        });
    });
});
</script>
</body>
</html>