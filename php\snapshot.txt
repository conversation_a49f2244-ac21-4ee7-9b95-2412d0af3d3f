This snapshot was automatically generated on
Wed, 02 Nov 2022 15:27:47 +0000

Version: 7.4.33
Branch: HEAD
Build: C:\php-snap-build\php74\vc15\x64\obj\Release

Built-in Extensions
===========================
Core
bcmath
calendar
ctype
date
filter
hash
iconv
json
SPL
pcre
readline
Reflection
session
standard
mysqlnd
tokenizer
zip
zlib
libxml
dom
PDO
openssl
SimpleXML
xml
wddx
xmlreader
xmlwriter
curl
ftp
sqlite3
Phar
mbstring
mysqli


Dependency information:
Module: php7.dll
===========================
	imagehlp.dll

Module: php_curl.dll
===========================
	libcrypto-1_1-x64.dll
	libssl-1_1-x64.dll
	libssh2.dll
	nghttp2.dll

Module: libssl-1_1-x64.dll
===========================
	libcrypto-1_1-x64.dll

Module: libssh2.dll
===========================
	libcrypto-1_1-x64.dll

Module: php_enchant.dll
===========================
	libenchant.dll

Module: libenchant.dll
===========================
	glib-2.dll
	gmodule-2.dll

Module: gmodule-2.dll
===========================
	glib-2.dll

Module: php_ftp.dll
===========================
	libcrypto-1_1-x64.dll
	libssl-1_1-x64.dll

Module: php_intl.dll
===========================
	icuuc66.dll
	icuin66.dll
	icuio66.dll

Module: icuuc66.dll
===========================
	icudt66.dll

Module: icuin66.dll
===========================
	icuuc66.dll

Module: icuio66.dll
===========================
	icuuc66.dll
	icuin66.dll

Module: php_ldap.dll
===========================
	libsasl.dll
	libcrypto-1_1-x64.dll
	libssl-1_1-x64.dll

Module: php_openssl.dll
===========================
	libcrypto-1_1-x64.dll
	libssl-1_1-x64.dll

Module: php_pgsql.dll
===========================
	libpq.dll

Module: libpq.dll
===========================
	libssl-1_1-x64.dll
	libcrypto-1_1-x64.dll

Module: php_snmp.dll
===========================
	libcrypto-1_1-x64.dll

Module: php_sodium.dll
===========================
	libsodium.dll

Module: php_sqlite3.dll
===========================
	libsqlite3.dll

Module: php_pdo_pgsql.dll
===========================
	libpq.dll

Module: php_pdo_sqlite.dll
===========================
	libsqlite3.dll

