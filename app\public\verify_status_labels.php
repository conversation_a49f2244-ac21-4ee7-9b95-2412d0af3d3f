<?php
// Verify status labels in the database
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

// Check if user is logged in and is admin
if (!isset($_COOKIE['user'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

try {
    echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px;background:#0b1220;color:#e2e8f0;}</style>";
    echo "<h2>Durum Etiketleri Doğrulama</h2>";
    
    // Check current status labels
    echo "<h3>Mevcut Durumlar:</h3>";
    $stmt = $pdo->query("SELECT id, code, label FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $allCorrect = true;
    
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-color:#1f2937;background:#0f172a;'>";
    echo "<tr><th>ID</th><th>Kod</th><th>Etiket</th><th>Durum</th></tr>";
    foreach ($statuses as $status) {
        $isCorrect = strpos($status['label'], 'Müdür') === false;
        if (!$isCorrect) {
            $allCorrect = false;
        }
        
        echo "<tr>";
        echo "<td>{$status['id']}</td>";
        echo "<td>{$status['code']}</td>";
        echo "<td>{$status['label']}</td>";
        echo "<td>" . ($isCorrect ? 
            "<span style='color:#4ade80;'>✓ Doğru</span>" : 
            "<span style='color:#f87171;'>✗ 'Müdür' içeriyor</span>") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($allCorrect) {
        echo "<div style='background:#1d3327;padding:16px;border-radius:8px;margin:16px 0;'>";
        echo "<h3 style='color:#4ade80;margin:0;'>✓ Tüm durum etiketleri doğru!</h3>";
        echo "<p>Sistem zaten 'Yönetici' terminolojisini kullanıyor.</p>";
        echo "</div>";
    } else {
        echo "<div style='background:#331d1d;padding:16px;border-radius:8px;margin:16px 0;'>";
        echo "<h3 style='color:#f87171;margin:0;'>✗ Bazı etiketler 'Müdür' içeriyor</h3>";
        echo "<p>Bu etiketlerin güncellenmesi gerekiyor.</p>";
        echo "</div>";
    }
    
    echo "<p><a href='/requests.php' style='color:#93c5fd;'>Ana sayfaya dön</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Hata</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>