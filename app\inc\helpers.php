<?php
// C:\workapp\app\inc\helpers.php

// Güvenli çıktı (XSS önlemek için)
function e($s) {
    return htmlspecialchars((string)$s, ENT_QUOTES, 'UTF-8');
}

// Şu anki tarih/saat (Y-m-d H:i:s)
function now() {
    return (new DateTime())->format('Y-m-d H:i:s');
}

// Yönlendirme
function redirect($url) {
    header('Location: '.$url);
    exit;
}

// CSRF Token oluşturma
function generate_csrf_token() {
    if (session_status() !== PHP_SESSION_ACTIVE) {
        session_start();
    }
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// CSRF Token doğrulama
function verify_csrf_token($token) {
    if (session_status() !== PHP_SESSION_ACTIVE) {
        session_start();
    }
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// CSRF Token HTML input
function csrf_token_input() {
    return '<input type="hidden" name="csrf_token" value="' . e(generate_csrf_token()) . '">';
}

// Input sanitization
function sanitize_input($input, $type = 'string') {
    $input = trim($input);

    switch ($type) {
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT) !== false ? (int)$input : 0;
        case 'float':
            return filter_var($input, FILTER_VALIDATE_FLOAT) !== false ? (float)$input : 0.0;
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL) !== false ? $input : '';
        case 'url':
            return filter_var($input, FILTER_VALIDATE_URL) !== false ? $input : '';
        case 'string':
        default:
            return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
}

// Validate required fields
function validate_required_fields($fields, $data) {
    $errors = [];
    foreach ($fields as $field => $label) {
        if (empty($data[$field])) {
            $errors[] = "$label alanı zorunludur.";
        }
    }
    return $errors;
}
