<?php
// Import requests from CSV
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

// Only allow Admin role to import
if ($me['role'] !== 'Admin') {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];
    
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $error = "Dosya yükleme hatası.";
        include 'import_requests_form.php';
        exit;
    }
    
    // Check file type
    $fileType = mime_content_type($file['tmp_name']);
    if ($fileType !== 'text/csv' && $fileType !== 'text/plain' && 
        $file['type'] !== 'text/csv' && $file['type'] !== 'application/vnd.ms-excel') {
        $error = "Geçersiz dosya türü. Lütfen CSV dosyası yükleyin.";
        include 'import_requests_form.php';
        exit;
    }
    
    // Process CSV file
    if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
        // Skip BOM if present
        $bom = fread($handle, 3);
        if ($bom !== chr(0xEF).chr(0xBB).chr(0xBF)) {
            rewind($handle);
        }
        
        // Read header row
        $header = fgetcsv($handle, 1000, ',');
        
        // Expected headers
        $expected_headers = [
            'ID', 'Oluşturulma Tarihi', 'Başlık', 'Açıklama', 'Birim', 
            'Çağrı Numarası', 'Öncelik', 'Durum', 'Talep Sahibi', 
            'Atanan Mühendis', 'Onaylayan Yönetici', 'Onay Tarihi', 
            'Planlanan Başlangıç', 'Planlanan Bitiş', 'Gerçek Başlangıç', 'Gerçek Bitiş'
        ];
        
        // Check if headers match
        if ($header !== $expected_headers) {
            $error = "CSV dosyası beklenen formatta değil. Lütfen doğru şablonu kullanın.";
            fclose($handle);
            include 'import_requests_form.php';
            exit;
        }
        
        // Begin transaction
        $pdo->beginTransaction();
        
        try {
            $imported_count = 0;
            $errors = [];
            
            // Prepare statements for lookups
            $priority_stmt = $pdo->prepare("SELECT id FROM Priorities WHERE name = ?");
            $status_stmt = $pdo->prepare("SELECT id FROM Status WHERE label = ?");
            $user_stmt = $pdo->prepare("SELECT id FROM Users WHERE display_name = ?");
            
            // Process each row
            while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                // Skip empty rows
                if (count($data) < count($expected_headers)) {
                    continue;
                }
                
                // Extract data
                list($id, $created_at, $title, $description, $unit, 
                     $call_number, $priority, $status, $creator, 
                     $assigned_engineer, $manager, $approved_at, 
                     $planned_start, $planned_finish, $actual_start, $actual_finish) = $data;
                
                // Skip if title is empty
                if (empty($title)) {
                    continue;
                }
                
                // Lookup priority ID
                $priority_stmt->execute([$priority]);
                $priority_row = $priority_stmt->fetch();
                $priority_id = $priority_row ? $priority_row['id'] : 2; // Default to Medium
                
                // Lookup status ID
                $status_stmt->execute([$status]);
                $status_row = $status_stmt->fetch();
                $status_id = $status_row ? $status_row['id'] : 1; // Default to New
                
                // Lookup creator ID
                $user_stmt->execute([$creator]);
                $creator_row = $user_stmt->fetch();
                $created_by = $creator_row ? $creator_row['id'] : $me['id']; // Default to current user
                
                // Lookup assigned engineer ID
                $assigned_engineer_id = null;
                if (!empty($assigned_engineer)) {
                    $user_stmt->execute([$assigned_engineer]);
                    $eng_row = $user_stmt->fetch();
                    $assigned_engineer_id = $eng_row ? $eng_row['id'] : null;
                }
                
                // Lookup manager ID
                $manager_id = null;
                if (!empty($manager)) {
                    $user_stmt->execute([$manager]);
                    $mgr_row = $user_stmt->fetch();
                    $manager_id = $mgr_row ? $mgr_row['id'] : null;
                }
                
                // Insert or update request
                if (!empty($id) && is_numeric($id)) {
                    // Update existing request
                    $stmt = $pdo->prepare("UPDATE Requests SET 
                        created_at = ?, title = ?, description = ?, unit = ?, call_number = ?,
                        priority_id = ?, status_id = ?, created_by = ?, assigned_engineer = ?,
                        manager_id = ?, approved_at = ?, planned_start = ?, planned_finish = ?,
                        actual_start = ?, actual_finish = ?
                        WHERE id = ?");
                    $stmt->execute([
                        $created_at, $title, $description, $unit, $call_number,
                        $priority_id, $status_id, $created_by, $assigned_engineer_id,
                        $manager_id, $approved_at, $planned_start, $planned_finish,
                        $actual_start, $actual_finish, $id
                    ]);
                } else {
                    // Insert new request
                    $stmt = $pdo->prepare("INSERT INTO Requests (
                        created_at, title, description, unit, call_number,
                        priority_id, status_id, created_by, assigned_engineer,
                        manager_id, approved_at, planned_start, planned_finish,
                        actual_start, actual_finish
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $created_at ?: now(), $title, $description, $unit, $call_number,
                        $priority_id, $status_id, $created_by, $assigned_engineer_id,
                        $manager_id, $approved_at, $planned_start, $planned_finish,
                        $actual_start, $actual_finish
                    ]);
                }
                
                $imported_count++;
            }
            
            fclose($handle);
            $pdo->commit();
            
            $success = "Başarıyla {$imported_count} talep içe aktarıldı.";
            include 'import_requests_form.php';
            exit;
            
        } catch (Exception $e) {
            $pdo->rollback();
            fclose($handle);
            $error = "İçe aktarma sırasında hata oluştu: " . $e->getMessage();
            include 'import_requests_form.php';
            exit;
        }
    } else {
        $error = "CSV dosyası açılamadı.";
        include 'import_requests_form.php';
        exit;
    }
}

include 'import_requests_form.php';
?>