<?php
// C:\workapp\app\public\request_view.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';
require_login();
$me = current_user();

$id = (int)($_GET['id'] ?? 0);
if(!$id){ echo "ID yok"; exit; }

$stmt = $pdo->prepare("SELECT r.*, s.label status_label, p.name priority_name,
  (SELECT display_name FROM Users WHERE id=r.assigned_engineer) AS assigned_engineer_name,
  (SELECT display_name FROM Users WHERE id=r.manager_id) AS manager_name
 FROM Requests r
 JOIN Status s ON s.id=r.status_id
 JOIN Priorities p ON p.id=r.priority_id
 WHERE r.id=?");
$stmt->execute([$id]);
$req = $stmt->fetch(PDO::FETCH_ASSOC);
if(!$req){ echo "Kayıt bulunamadı"; exit; }

/* Son log (geri al için) */
$lastLog = $pdo->prepare("SELECT * FROM StatusLog WHERE request_id=? ORDER BY id DESC LIMIT 1");
$lastLog->execute([$id]);
$last = $lastLog->fetch(PDO::FETCH_ASSOC);

/* POST işlemleri */
if ($_SERVER['REQUEST_METHOD']==='POST') {

  // Mühendis: NEW aşamasında değerlendirme yazıp yöneticinin onayına gönderir
  if (isset($_POST['to_pending_manager']) && $me['role']==='Engineer' && $req['status_id']==1) {
    $note = "Mühendis değerlendirmesi: ".trim($_POST['eng_note'] ?? '');
    $pdo->beginTransaction();
    // atanan mühendis ben olayım
    $pdo->prepare("UPDATE Requests SET status_id=3, assigned_engineer=? WHERE id=?")->execute([$me['id'],$id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],3,$me['id'],now(),$note]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: onayla (not opsiyonel)
  if (isset($_POST['approve']) && $me['role']==='Manager' && $req['status_id']==3) {
    $note = trim($_POST['mgr_note'] ?? '');
    $note = $note!=='' ? "Yönetici onayı: ".$note : "Yönetici onayı verildi";
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=4, manager_id=?, approved_at=? WHERE id=?")
         ->execute([$me['id'], now(), $id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],4,$me['id'],now(),$note]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: reddet (gerekçe zorunlu)
  if (isset($_POST['reject']) && $me['role']==='Manager' && $req['status_id']==3) {
    $reason = trim($_POST['mgr_reason'] ?? '');
    if ($reason==='') { $reason = 'Gerekçe verilmedi'; }
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=7 WHERE id=?")->execute([$id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],7,$me['id'],now(),'Yönetici reddetti: '.$reason]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Mühendis: işi başlat
  if (isset($_POST['start']) && $me['role']==='Engineer' && $req['status_id']==4) {
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=5, actual_start=? WHERE id=?")->execute([now(), $id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],5,$me['id'],now(),'İş başlatıldı (Devam Ediyor)']);
    $pdo->commit(); redirect("/request_view.php?id=$id");
  }

  // Mühendis: işi tamamla
  if (isset($_POST['done']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=8 WHERE id=?")->execute([$id]); // Changed to status 8 (PENDING_COMPLETION)
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],8,$me['id'],now(),'İş tamamlama talebi oluşturuldu']); // Changed to status 8
    $pdo->commit(); redirect("/request_view.php?id=$id");
  }

  // Yönetici: işi tamamla (onayla)
  if (isset($_POST['approve_completion']) && $me['role']==='Manager' && $req['status_id']==8) { // New status 8 (PENDING_COMPLETION)
    $completion_note = trim($_POST['completion_note'] ?? '');
    $pdo->beginTransaction();
    try {
      // Update request status to DONE
      $pdo->prepare("UPDATE Requests SET status_id=6, actual_finish=? WHERE id=?")
          ->execute([now(), $id]);
      
      // Log the completion approval
      $note = 'Yönetici işi tamamladı olarak onayladı';
      if ($completion_note !== '') {
        $note .= ': ' . $completion_note;
      }
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id, 8, 6, $me['id'], now(), $note]);
      
      $pdo->commit();
    } catch (Exception $e) {
      $pdo->rollback();
      throw $e;
    }
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: işi reddet
  if (isset($_POST['reject_completion']) && $me['role']==='Manager' && $req['status_id']==8) { // New status 8 (PENDING_COMPLETION)
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    if ($rejection_reason==='') { $rejection_reason = 'Gerekçe verilmedi'; }
    $pdo->beginTransaction();
    try {
      // Revert request status back to IN_PROGRESS
      $pdo->prepare("UPDATE Requests SET status_id=5 WHERE id=?")->execute([$id]);
      
      // Log the rejection
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id, 8, 5, $me['id'], now(), 'Yönetici tamamlamayı reddetti: '.$rejection_reason]);
      
      $pdo->commit();
    } catch (Exception $e) {
      $pdo->rollback();
      throw $e;
    }
    redirect("/request_view.php?id=$id");
  }

  // Geri al: mevcut durumu bir önceki logdaki old_status_id'ye çevir
  if (isset($_POST['revert']) && $last && in_array($req['status_id'],[3,4])) {
    $prev = (int)$last['old_status_id'];
    if ($prev>0) {
      $pdo->beginTransaction();
      $pdo->prepare("UPDATE Requests SET status_id=? WHERE id=?")->execute([$prev,$id]);
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],$prev,$me['id'],now(),'Geri alındı']);
      $pdo->commit();
    }
    redirect("/request_view.php?id=$id");
  }

  // Adam/saat
  if (isset($_POST['add_worker']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $stmt=$pdo->prepare("INSERT INTO WorkerAssignments(request_id,worker_id,date,hours,note) VALUES(?,?,?,?,?)");
    $stmt->execute([$id,(int)$_POST['worker_id'],$_POST['date'],(float)$_POST['hours'],trim($_POST['note'] ?? '')]);
    redirect("/request_view.php?id=$id#workers");
  }

  // Malzeme
  if (isset($_POST['add_material']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $stmt=$pdo->prepare("INSERT INTO MaterialUsage(request_id,material_id,quantity,unit,note) VALUES(?,?,?,?,?)");
    $stmt->execute([$id,(int)$_POST['material_id'],(float)$_POST['quantity'],trim($_POST['unit']),trim($_POST['note'] ?? '')]);
    redirect("/request_view.php?id=$id#materials");
  }
}

/* Görünümler */
$workers = $pdo->query("SELECT id, full_name FROM Workers WHERE is_active=1 ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);
$materials = $pdo->query("SELECT id, COALESCE(code||' - ','') || name AS label, unit FROM Materials ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

$logsStmt = $pdo->prepare("SELECT l.*, u.display_name FROM StatusLog l JOIN Users u ON u.id=l.changed_by WHERE l.request_id=? ORDER BY l.id DESC");
$logsStmt->execute([$id]); $history = $logsStmt->fetchAll(PDO::FETCH_ASSOC);

$wa = $pdo->prepare("SELECT a.date, w.full_name, a.hours, a.note
                     FROM WorkerAssignments a JOIN Workers w ON w.id=a.worker_id
                     WHERE a.request_id=? ORDER BY a.id DESC");
$wa->execute([$id]); $assignments = $wa->fetchAll(PDO::FETCH_ASSOC);

$mu = $pdo->prepare("SELECT m.name, COALESCE(m.code,'') AS code, u.quantity, u.unit, u.note
                     FROM MaterialUsage u JOIN Materials m ON m.id=u.material_id
                     WHERE u.request_id=? ORDER BY u.id DESC");
$mu->execute([$id]); $mat_used = $mu->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>İş #<?=$id?> • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--panel:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;--line:#1f2937;}
    *{box-sizing:border-box} body{margin:0;background:#0b1220;color:var(--text);font:15px/1.55 system-ui,Segoe UI,Arial}
    header{display:flex;justify-content:space-between;align-items:center;padding:18px 22px;border-bottom:1px solid var(--line);background:#0f172a;position:sticky;top:0}
    main{max-width:1100px;margin:18px auto;padding:0 22px}
    a{color:#93c5fd}
    .card{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:16px;margin:14px 0}
    h2{margin:0 0 12px;font-size:18px}
    label{display:block;margin:8px 0;color:#cbd5e1}
    input,select,textarea{width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px}
    textarea{min-height:90px}
    button{padding:10px 14px;border-radius:10px;border:0;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    table{width:100%;border-collapse:collapse;margin-top:12px}
    th,td{border-bottom:1px solid #1f2937;padding:10px;text-align:left}
    tr:hover td{background:#0b1725}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    .btns form{display:inline-block;margin-right:8px}
    .muted{color:#94a3b8}
  </style>
</head>
<body>
<header>
  <div class="row">
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
    <a href="/requests.php">← Talepler</a>
  </div>
  <div>Talepler</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <section class="card">
    <div class="row" style="justify-content:space-between;align-items:flex-start">
      <div>
        <div><b>Durum:</b> <?=e($req['status_label'])?> &nbsp; | &nbsp; <b>Öncelik:</b> <?=e($req['priority_name'])?> &nbsp; | &nbsp; <b>Birim:</b> <?=e($req['unit'])?></div>
        <div class="muted" style="margin-top:6px"><?=nl2br(e($req['description']))?></div>
      </div>
    </div>
  </section>

  <section class="card">
    <h2>Akış Aksiyonları</h2>
    <div class="btns">

    <?php if($me['role']==='Engineer' && $req['status_id']==1): ?>
      <!-- Mühendis değerlendirmesi -->
      <form method="post" class="row" style="gap:12px">
        <input type="hidden" name="to_pending_manager" value="1">
        <label style="flex:1 1 100%">Mühendis Değerlendirmesi
          <textarea name="eng_note" placeholder="Yapılabilirlik, ön koşullar, riskler..."></textarea>
        </label>
        <button>Yönetici Onayına Gönder</button>
      </form>
    <?php endif; ?>

    <?php if($me['role']==='Manager' && $req['status_id']==3): ?>
      <form method="post" class="row">
        <label style="flex:1 1 280px">Not (opsiyonel)
          <input name="mgr_note" placeholder="Onay notu (opsiyonel)">
        </label>
        <button name="approve">Onayla</button>
      </form>
      <form method="post" class="row" style="margin-top:8px">
        <label style="flex:1 1 280px">Gerekçe (zorunlu)
          <input name="mgr_reason" required placeholder="Red gerekçesi">
        </label>
        <button name="reject">Reddet</button>
      </form>
    <?php endif; ?>

    <?php if($me['role']==='Engineer' && $req['status_id']==4): ?>
      <form method="post"><button name="start">İşi Başlat (Devam Ediyor)</button></form>
    <?php endif; ?>

    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
      <form method="post"><button name="done">Tamamla</button></form>
    <?php endif; ?>

    <?php if($me['role']==='Manager' && $req['status_id']==8): // New status 8 (PENDING_COMPLETION) ?>
      <div class="card" style="background:#1e293b">
        <h2>İş Tamamlama Onayı</h2>
        <p>Bu iş mühendis tarafından tamamlandı olarak işaretlendi. Lütfen inceleyip onaylayın veya reddedin.</p>
        
        <form method="post" class="row">
          <label style="flex:1 1 100%">Onay Notu (İsteğe Bağlı)
            <textarea name="completion_note" placeholder="Onay notu (isteğe bağlı)"></textarea>
          </label>
          <button name="approve_completion" style="background:#22c55e">İşi Tamamlandı Olarak Onayla</button>
        </form>
        
        <form method="post" class="row" style="margin-top:10px">
          <label style="flex:1 1 100%">Reddetme Gerekçesi (Zorunlu)
            <textarea name="rejection_reason" placeholder="Reddetme gerekçesi" required></textarea>
          </label>
          <button name="reject_completion" style="background:#ef4444;color:white">İşi Reddet ve Geri Gönder</button>
        </form>
      </div>
    <?php endif; ?>

    <?php if(in_array($req['status_id'],[3,4])): ?>
      <form method="post"><button name="revert">Geri Al (Önceki Duruma)</button></form>
    <?php endif; ?>

    </div>
  </section>

  <section class="card" id="workers">
    <h2>Adam/Saat Girişi</h2>
    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
    <form method="post" class="row">
      <input type="hidden" name="add_worker" value="1">
      <label style="flex:1 1 160px">Tarih <input type="date" name="date" required value="<?=date('Y-m-d')?>"></label>
      <label style="flex:2 1 260px">Çalışan
        <select name="worker_id">
          <?php foreach($workers as $w): ?><option value="<?=$w['id']?>"><?=e($w['full_name'])?></option><?php endforeach; ?>
        </select>
      </label>
      <label style="flex:1 1 120px">Saat <input type="number" step="0.25" min="0" name="hours" required></label>
      <label style="flex:2 1 260px">Not <input name="note"></label>
      <button>Ekle</button>
    </form>
    <?php else: ?><i class="muted">Adam/saat girişi yalnızca <b>Devam Ediyor</b> durumunda.</i><?php endif; ?>

    <?php if($assignments): ?>
    <table>
      <tr><th>Tarih</th><th>Çalışan</th><th>Saat</th><th>Not</th></tr>
      <?php foreach($assignments as $r): ?>
      <tr><td><?=e($r['date'])?></td><td><?=e($r['full_name'])?></td><td><?=e($r['hours'])?></td><td><?=e($r['note'])?></td></tr>
      <?php endforeach; ?>
    </table>
    <?php endif; ?>
  </section>

  <section class="card" id="materials">
    <h2>Malzeme Kullanımı</h2>
    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
    <form method="post" class="row">
      <input type="hidden" name="add_material" value="1">
      <label style="flex:2 1 260px">Malzeme
        <select name="material_id">
          <?php foreach($materials as $m): ?><option value="<?=$m['id']?>"><?=e($m['label'])?></option><?php endforeach; ?>
        </select>
      </label>
      <label style="flex:1 1 140px">Miktar <input type="number" step="0.01" min="0" name="quantity" required></label>
      <label style="flex:1 1 120px">Birim <input name="unit" value="<?=e($materials[0]['unit'] ?? '')?>" required></label>
      <label style="flex:2 1 260px">Not <input name="note"></label>
      <button>Ekle</button>
    </form>
    <?php else: ?><i class="muted">Malzeme girişi yalnızca <b>Devam Ediyor</b> durumunda.</i><?php endif; ?>

    <?php if($mat_used): ?>
    <table>
      <tr><th>Kod</th><th>Malzeme</th><th>Miktar</th><th>Birim</th><th>Not</th></tr>
      <?php foreach($mat_used as $r): ?>
      <tr>
        <td><?=e($r['code'])?></td>
        <td><?=e($r['name'])?></td>
        <td><?=e($r['quantity'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php endif; ?>
  </section>

  <section class="card">
    <h2>Durum Günlüğü</h2>
    <?php if($history): ?>
      <ul style="margin:6px 0 0 18px">
        <?php foreach($history as $h): ?>
          <li><?=e($h['changed_at'])?> — <?=e($h['display_name'])?>: <?=e($h['note'])?></li>
        <?php endforeach; ?>
      </ul>
    <?php else: ?><i class="muted">Günlük kaydı yok.</i><?php endif; ?>
  </section>
</main>

<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});
</script>
</body>
</html>
