<?php
// C:\workapp\app\public\export.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
require_role(['Admin', 'Manager']);

$type = $_GET['type'] ?? '';
$start_date = $_GET['start'] ?? '';
$end_date = $_GET['end'] ?? '';

// Filtre parametreleri
$unit_filter = $_GET['unit'] ?? '';
$engineer_filter = $_GET['engineer'] ?? '';
$status_filter = $_GET['status'] ?? '';

if (!$type || !$start_date || !$end_date) {
    http_response_code(400);
    echo "Eksik parametre: type, start ve end zorunludur.";
    exit;
}

if ($type === 'hours') {
    // Adam/saat raporu
    $query = "
        SELECT w.full_name,
               SUM(a.hours) AS total_hours
        FROM WorkerAssignments a
        JOIN Workers w ON w.id=a.worker_id
        JOIN Requests r ON r.id=a.request_id
        WHERE a.date BETWEEN :start AND :end
    ";

    $params = [':start' => $start_date, ':end' => $end_date];

    // Filtreleri uygula
    if ($unit_filter) {
        $query .= " AND r.unit = :unit";
        $params[':unit'] = $unit_filter;
    }

    if ($engineer_filter) {
        $query .= " AND r.assigned_engineer = :engineer";
        $params[':engineer'] = $engineer_filter;
    }

    if ($status_filter) {
        $query .= " AND r.status_id = :status";
        $params[':status'] = $status_filter;
    }

    $query .= " GROUP BY w.id ORDER BY total_hours DESC";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // CSV başlığını ayarla
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=adam_saat_raporu_'.$start_date.'_'. $end_date.'.csv');

    // CSV çıktısı oluştur
    $output = fopen('php://output', 'w');
    
    // UTF-8 BOM (Excel'de Türkçe karakter sorununu çözmek için)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Başlık satırı
    fputcsv($output, ['Çalışan', 'Toplam Saat'], ';');

    // Veri satırları
    foreach ($results as $row) {
        fputcsv($output, [
            $row['full_name'],
            number_format((float)$row['total_hours'], 2, ',', '')
        ], ';');
    }

    fclose($output);
    exit;
} 
elseif ($type === 'materials') {
    // Malzeme tüketimi raporu
    $query = "
        SELECT m.code, m.name, m.unit,
               SUM(u.quantity) AS total_qty
        FROM MaterialUsage u
        JOIN Materials m ON m.id=u.material_id
        JOIN Requests r ON r.id=u.request_id
        WHERE r.created_at BETWEEN :start AND :end
    ";

    $params = [':start' => $start_date.' 00:00:00', ':end' => $end_date.' 23:59:59'];

    // Filtreleri uygula
    if ($unit_filter) {
        $query .= " AND r.unit = :unit";
        $params[':unit'] = $unit_filter;
    }

    if ($engineer_filter) {
        $query .= " AND r.assigned_engineer = :engineer";
        $params[':engineer'] = $engineer_filter;
    }

    if ($status_filter) {
        $query .= " AND r.status_id = :status";
        $params[':status'] = $status_filter;
    }

    $query .= " GROUP BY m.id ORDER BY total_qty DESC";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // CSV başlığını ayarla
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=malzeme_tuketimi_'.$start_date.'_'. $end_date.'.csv');

    // CSV çıktısı oluştur
    $output = fopen('php://output', 'w');
    
    // UTF-8 BOM (Excel'de Türkçe karakter sorununu çözmek için)
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Başlık satırı
    fputcsv($output, ['Kod', 'Malzeme', 'Birim', 'Toplam Miktar'], ';');

    // Veri satırları
    foreach ($results as $row) {
        fputcsv($output, [
            $row['code'],
            $row['name'],
            $row['unit'],
            number_format((float)$row['total_qty'], 2, ',', '')
        ], ';');
    }

    fclose($output);
    exit;
} 
else {
    http_response_code(400);
    echo "Geçersiz tip. Sadece 'hours' veya 'materials' desteklenmektedir.";
    exit;
}