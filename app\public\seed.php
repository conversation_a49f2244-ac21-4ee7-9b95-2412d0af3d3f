<?php
// C:\workapp\app\public\seed.php
require __DIR__.'/../inc/db.php';

function addUser($dn,$un,$pw,$role,$unit){
  global $pdo;
  $stmt = $pdo->prepare("INSERT OR IGNORE INTO Users(display_name,username,password_hash,role_id,unit)
                         VALUES(?,?,?,?,?)");
  $stmt->execute([$dn,$un,password_hash($pw,PASSWORD_DEFAULT),$role,$unit]);
}
addUser('Admin','admin','Admin!23',1,'Genel');
addUser('<PERSON><PERSON><PERSON><PERSON><PERSON>','mudur','<PERSON><PERSON><PERSON>!23',2,'Bakım');
addUser('Mühend<PERSON>','muhendis','Muhend<PERSON>!23',3,'Bakım');

$pdo->exec("INSERT OR IGNORE INTO Workers(id, sicil_no, full_name, trade, is_active) VALUES
 (1,'S001','<PERSON><PERSON> Yılmaz','El<PERSON><PERSON>k',1),
 (2,'S002','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',1),
 (3,'S003','<PERSON>y<PERSON>e <PERSON><PERSON>','<PERSON>n<PERSON>aat',1),
 (4,'S004','Fatma Şahin','Elektrik',1),
 (5,'S005','<PERSON> Çelik','<PERSON>kanik',1),
 (6,'S006','<PERSON>ine Yıldız','<PERSON>n<PERSON>aat',1),
 (7,'S007','<PERSON> <PERSON>zkan','Elektrik',1),
 (8,'S008','Hatice Koç','Mekanik',1),
 (9,'S009','İsmail Aksoy','İnşaat',1),
 (10,'S010','Zeynep Güler','Elektrik',1),
 (11,'S011','Hasan Aydın','Mekanik',1),
 (12,'S012','Burcu Özdemir','İnşaat',1),
 (13,'S013','Murat Karadağ','Elektrik',1),
 (14,'S014','Selin Taş','Mekanik',1),
 (15,'S015','Canan Çetin','İnşaat',1)");

$pdo->exec("INSERT OR IGNORE INTO Materials(id,code,name,unit) VALUES
 (1,'KAB-1.5','Kablo 1.5mm²','m'),
 (2,'CIV-M8','Cıvata M8','adet'),
 (3,'BOYA-7021','End. Boya RAL7021','lt')");

// Add disciplines
$pdo->exec("INSERT OR IGNORE INTO Disciplines(id,name) VALUES
 (1,'Elektrik'),
 (2,'Mekanik'),
 (3,'İnşaat')");

echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px}</style>";
echo "<h3>OK — seed verileri eklendi.</h3>";
echo "<p><a href='/login.php'>Girişe dön</a></p>";