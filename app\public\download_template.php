<?php
// Download CSV template for importing requests
require __DIR__.'/../inc/auth.php';

require_login();
$me = current_user();

// Only allow Admin role to download template
if ($me['role'] !== 'Admin') {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=requests_template.csv');

// Open output stream
$output = fopen('php://output', 'w');

// Add BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write CSV header
fputcsv($output, [
    'ID',
    'Oluşturulma Tarihi',
    'Başlık',
    'Açıklama',
    'Birim',
    'Çağrı Numarası',
    'Öncelik',
    '<PERSON><PERSON>',
    '<PERSON><PERSON> Sahib<PERSON>',
    '<PERSON><PERSON><PERSON> M<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>ö<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>lana<PERSON>ş<PERSON>',
    'Planlanan Bitiş',
    'Gerçek Başlangıç',
    'Gerçek Bitiş'
]);

// Write example row
fputcsv($output, [
    '', // ID (leave empty for new requests)
    date('Y-m-d H:i:s'), // Oluşturulma Tarihi
    'Örnek Talep Başlığı', // Başlık
    'Örnek talep açıklaması', // Açıklama
    'Üretim', // Birim
    'Ç-0001', // Çağrı Numarası
    'Orta', // Öncelik
    'Yeni', // Durum
    $me['display_name'], // Talep Sahibi
    '', // Atanan Mühendis
    '', // Onaylayan Yönetici
    '', // Onay Tarihi
    date('Y-m-d'), // Planlanan Başlangıç
    date('Y-m-d', strtotime('+7 days')), // Planlanan Bitiş
    '', // Gerçek Başlangıç
    '' // Gerçek Bitiş
]);

fclose($output);
exit;
?>