<?php
// Direct database update script for status labels
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

// Check if user is logged in and is admin
if (!isset($_COOKIE['user'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

try {
    echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px;background:#0b1220;color:#e2e8f0;}</style>";
    echo "<h2>Durum Etiketleri Güncelleme</h2>";
    
    // Check current status labels
    echo "<h3>Mevcut Durumlar:</h3>";
    $stmt = $pdo->query("SELECT id, code, label FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-color:#1f2937;background:#0f172a;'>";
    echo "<tr><th>ID</th><th>Kod</th><th>Etiket</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr>";
        echo "<td>{$status['id']}</td>";
        echo "<td>{$status['code']}</td>";
        echo "<td>{$status['label']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Update any status with "Müdür" to "Yönetici"
    echo "<h3>Güncelleniyor...</h3>";
    $stmt = $pdo->prepare("UPDATE Status SET label = REPLACE(label, 'Müdür', 'Yönetici') WHERE label LIKE '%Müdür%'");
    $stmt->execute();
    $updated = $stmt->rowCount();
    
    if ($updated > 0) {
        echo "<p><strong>{$updated} adet durum etiketi güncellendi.</strong></p>";
    } else {
        echo "<p><strong>Herhangi bir etiketin güncellenmesine gerek yok.</strong></p>";
    }
    
    // Verify the update
    echo "<h3>Güncellenmiş Durumlar:</h3>";
    $stmt = $pdo->query("SELECT id, code, label FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-color:#1f2937;background:#0f172a;'>";
    echo "<tr><th>ID</th><th>Kod</th><th>Etiket</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr>";
        echo "<td>{$status['id']}</td>";
        echo "<td>{$status['code']}</td>";
        echo "<td>{$status['label']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='/requests.php' style='color:#93c5fd;'>Ana sayfaya dön</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Hata</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}
?>