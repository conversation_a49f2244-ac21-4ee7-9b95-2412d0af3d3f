<?php
// C:\workapp\app\inc\auth.php

// Oturum açık değilse başlat
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Giriş zorunlu fonksiyonu
function require_login() {
    if (empty($_SESSION['uid'])) {
        header('Location: /login.php');
        exit;
    }
}

// Mevcut kullanıcı bilgisi (id, ad, rol)
function current_user() {
    return [
        'id'   => $_SESSION['uid'] ?? null,
        'name' => $_SESSION['name'] ?? null,
        'role' => $_SESSION['role'] ?? null,
    ];
}

// Rol kontrolü (örn. sadece Manager/Mühendis er<PERSON>)
function require_role($roles = []) {
    if (!$roles) return;
    $cur = $_SESSION['role'] ?? null;
    if (!in_array($cur, $roles)) {
        http_response_code(403);
        echo "Bu sayfaya eri<PERSON>im yet<PERSON> yok.";
        exit;
    }
}
