<?php
// Import requests form
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

// Only allow Admin role to import
if ($me['role'] !== 'Admin') {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Talep İçe Aktar • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--panel:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;--line:#1f2937;}
    *{box-sizing:border-box} body{margin:0;background:#0b1220;color:var(--text);font:15px/1.55 system-ui,<PERSON><PERSON><PERSON> UI,Arial}
    header{display:flex;justify-content:space-between;align-items:center;padding:18px 22px;border-bottom:1px solid var(--line);background:#0f172a;position:sticky;top:0}
    main{max-width:800px;margin:18px auto;padding:0 22px}
    a{color:#93c5fd}
    .card{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:16px;margin:14px 0}
    h2{margin:0 0 12px;font-size:18px}
    label{display:block;margin:8px 0;color:#cbd5e1}
    input,select,textarea{width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px}
    textarea{min-height:90px}
    button{padding:10px 14px;border-radius:10px;border:0;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    table{width:100%;border-collapse:collapse;margin-top:12px}
    th,td{border-bottom:1px solid #1f2937;padding:10px;text-align:left}
    tr:hover td{background:#0b1725}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    .btns form{display:inline-block;margin-right:8px}
    .muted{color:#94a3b8}
    .error{background:#331d1d;color:#f87171;padding:12px;border-radius:8px;margin:12px 0}
    .success{background:#1d3327;color:#4ade80;padding:12px;border-radius:8px;margin:12px 0}
  </style>
</head>
<body>
<header>
  <div class="row">
    <a href="/requests.php">← Talepler</a>
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
  </div>
  <div>Talep İçe Aktar</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <section class="card">
    <h2>CSV Şablonunu İndir</h2>
    <p>Talepleri içe aktarmak için aşağıdaki şablonu kullanabilirsiniz:</p>
    <a href="/download_template.php" style="display:inline-block;padding:10px 14px;background:#0ea5e9;color:white;border-radius:10px;text-decoration:none;margin:10px 0">
      CSV Şablonunu İndir
    </a>
  </section>
  
  <section class="card">
    <h2>Talepleri İçe Aktar</h2>
    <?php if (isset($error)): ?>
      <div class="error"><?=e($error)?></div>
    <?php endif; ?>
    
    <?php if (isset($success)): ?>
      <div class="success"><?=e($success)?></div>
    <?php endif; ?>
    
    <form method="post" enctype="multipart/form-data">
      <label>Dosya Seç
        <input type="file" name="csv_file" accept=".csv,text/csv" required>
      </label>
      <p class="muted">Yalnızca CSV dosyaları kabul edilir. Lütfen önce şablonu indirip doldurun.</p>
      <button type="submit">İçe Aktar</button>
    </form>
  </section>
  
  <section class="card">
    <h2>Nasıl Çalışır?</h2>
    <ol>
      <li>"CSV Şablonunu İndir" bağlantısına tıklayarak örnek şablonu indirin</li>
      <li>Şablonu bir elektronik tablo programında açın (Excel, Google Sheets, vs.)</li>
      <li>Talep bilgilerinizi satır satır girin</li>
      <li>Dosyayı CSV formatında kaydedin</li>
      <li>Bu sayfadan dosyanızı seçip "İçe Aktar" butonuna tıklayın</li>
    </ol>
    <p class="muted">Not: Varolan taleplerin ID'si ile eşleşen satırlar güncellenir, yeni ID'ler için yeni talepler oluşturulur.</p>
  </section>
</main>
</body>
</html>