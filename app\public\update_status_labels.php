<?php
// Update status labels in the database
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';

// Check if user is logged in and is admin
if (!isset($_COOKIE['user'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Connect to database
try {
    // Update any existing status records that might have the old label
    $stmt = $pdo->prepare("UPDATE Status SET label = 'Yönetici Onayı Bekliyor' WHERE code = 'PENDING_MANAGER'");
    $stmt->execute();
    $updated = $stmt->rowCount();
    
    echo "<style>body{font-family:system-ui,Segoe UI,Arial;padding:24px;background:#0b1220;color:#e2e8f0;}</style>";
    echo "<h3>Durum Etiketleri Güncellendi</h3>";
    
    if ($updated > 0) {
        echo "<p>{$updated} adet durum kaydı güncellendi.</p>";
    } else {
        echo "<p>Zaten güncel durum etiketleri kullanılıyor.</p>";
    }
    
    // Display all current statuses
    echo "<h4>Mevcut Durumlar:</h4>";
    $stmt = $pdo->query("SELECT id, code, label FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-color:#1f2937;background:#0f172a;'>";
    echo "<tr><th>ID</th><th>Kod</th><th>Etiket</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr>";
        echo "<td>{$status['id']}</td>";
        echo "<td>{$status['code']}</td>";
        echo "<td>{$status['label']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='/requests.php' style='color:#93c5fd;'>Ana sayfaya dön</a></p>";
    
} catch (Exception $e) {
    http_response_code(500);
    echo "Hata: " . htmlspecialchars($e->getMessage());
}
?>