/* WorkApp Modern Design System */

:root {
  /* Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;
  --text-inverse: #0f172a;
  
  --accent-primary: #3b82f6;
  --accent-secondary: #6366f1;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
  --accent-info: #06b6d4;
  
  --border-primary: #475569;
  --border-secondary: #64748b;
  --border-light: #334155;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--accent-primary), #2563eb);
  --gradient-secondary: linear-gradient(135deg, var(--accent-secondary), #4f46e5);
  --gradient-success: linear-gradient(135deg, var(--accent-success), #059669);
  --gradient-warning: linear-gradient(135deg, var(--accent-warning), #d97706);
  --gradient-danger: linear-gradient(135deg, var(--accent-danger), #dc2626);
  --gradient-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  
  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 20px 50px rgba(0, 0, 0, 0.4);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-full: 9999px;
  
  /* Typography */
  --font-family: 'Segoe UI', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.85rem;
  --font-size-base: 0.95rem;
  --font-size-lg: 1.1rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* Reset & Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--gradient-bg);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout Components */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

.container-sm {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

.container-lg {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
}

/* Header */
.header {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-lg) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-md);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.header-nav {
  display: flex;
  gap: var(--space-xl);
  align-items: center;
}

.header-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-base);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
}

.header-nav a:hover {
  color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-user {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.user-info {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.logout-btn {
  color: var(--accent-danger);
  text-decoration: none;
  font-weight: 500;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-sm);
  transition: var(--transition-base);
  font-size: var(--font-size-sm);
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  transform: translateY(-1px);
}

/* Main Content */
.main {
  padding: var(--space-xl) 0;
}

/* Cards */
.card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  transition: var(--transition-base);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
  opacity: 0.6;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
  gap: var(--space-md);
}

.card-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: var(--space-sm) 0 0;
}

.card-compact {
  padding: var(--space-lg);
}

.card-large {
  padding: var(--space-2xl);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-md);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: var(--transition-base);
  font-family: var(--font-family);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-row {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
  align-items: end;
}

.form-col {
  flex: 1;
  min-width: 200px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: var(--transition-base);
  text-decoration: none;
  font-family: var(--font-family);
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
}

.btn-secondary:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
}

.btn-success:hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.btn-warning {
  background: var(--gradient-warning);
  color: white;
}

.btn-warning:hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.btn-danger {
  background: var(--gradient-danger);
  color: white;
}

.btn-danger:hover {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
}

.btn-outline:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-lg);
}

.btn-block {
  width: 100%;
}

/* Tables */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
}

.table th,
.table td {
  padding: var(--space-md);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.table th {
  background: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  color: var(--text-secondary);
}

.table tr:hover td {
  background: rgba(59, 130, 246, 0.05);
}

.table tr:last-child td {
  border-bottom: none;
}

/* Badges */
.badge {
  display: inline-block;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: rgba(59, 130, 246, 0.2);
  color: var(--accent-primary);
}

.badge-success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--accent-success);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--accent-warning);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.2);
  color: var(--accent-danger);
}

.badge-info {
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-info);
}

.badge-muted {
  background: rgba(100, 116, 139, 0.2);
  color: var(--text-muted);
}

/* Alerts */
.alert {
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-lg);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--accent-success);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--accent-warning);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--accent-danger);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-info {
  background: rgba(6, 182, 212, 0.1);
  color: var(--accent-info);
  border: 1px solid rgba(6, 182, 212, 0.2);
}

/* Stats */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  text-align: center;
  transition: var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-sm);
}

.stat-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }

.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }

.p-0 { padding: 0; }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

/* Responsive Design */
@media (max-width: 768px) {
  .container,
  .container-sm,
  .container-lg {
    padding: 0 var(--space-md);
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-md);
  }
  
  .header-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--space-md);
  }
  
  .header-title {
    text-align: center;
    order: -1;
  }
  
  .header-user {
    justify-content: center;
  }
  
  .card {
    padding: var(--space-lg);
  }
  
  .card-large {
    padding: var(--space-xl);
  }
  
  .form-row {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .form-col {
    min-width: auto;
  }
  
  .table {
    font-size: var(--font-size-sm);
  }
  
  .table th,
  .table td {
    padding: var(--space-sm);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-sm);
  }
  
  .stat-card {
    padding: var(--space-md);
  }
  
  .stat-number {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .header-nav a {
    padding: var(--space-sm);
    font-size: var(--font-size-xs);
  }
  
  .card {
    padding: var(--space-md);
    margin-bottom: var(--space-md);
  }
  
  .card-title {
    font-size: var(--font-size-xl);
  }
  
  .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}
