<?php
require __DIR__.'/../inc/db.php';

echo "<pre>";

try {
    $pdo->exec('ALTER TABLE Materials ADD COLUMN discipline TEXT');
    echo "Discipline column added successfully to Materials table.\n";
} catch (Exception $e) {
    echo "Error adding discipline column: " . $e->getMessage() . "\n";
}

try {
    // Add disciplines table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS Disciplines (
        id INTEGER PRIMARY KEY,
        name TEXT UNIQUE NOT NULL
    )");
    echo "Disciplines table checked/created successfully.\n";
    
    // Insert default disciplines
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO Disciplines(id,name) VALUES (1,'Elektrik'),(2,'<PERSON><PERSON><PERSON>'),(3,'İn<PERSON>aat')");
    $stmt->execute();
    echo "Default disciplines inserted.\n";
} catch (Exception $e) {
    echo "Error with Disciplines table: " . $e->getMessage() . "\n";
}

echo "</pre>";
echo "<p><a href='/'>Ana <PERSON></a></p>";