<?php
// C:\workapp\app\public\request_edit.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

$id = (int)($_GET['id'] ?? 0);
if(!$id){ echo "ID yok"; exit; }

// Get request details
$stmt = $pdo->prepare("SELECT r.*, s.label status_label, p.name priority_name,
  (SELECT display_name FROM Users WHERE id=r.assigned_engineer) AS assigned_engineer_name,
  (SELECT display_name FROM Users WHERE id=r.manager_id) AS manager_name
 FROM Requests r
 JOIN Status s ON s.id=r.status_id
 JOIN Priorities p ON p.id=r.priority_id
 WHERE r.id=?");
$stmt->execute([$id]);
$req = $stmt->fetch(PDO::FETCH_ASSOC);
if(!$req){ echo "Kayıt bulunamadı"; exit; }

// Check if user has permission to edit this request
if($me['role'] !== 'Engineer' && $req['created_by'] != $me['id']) {
    http_response_code(403);
    echo "Bu talebi düzenleme yetkiniz yok.";
    exit;
}

// Get workers and materials for forms
$workers = $pdo->query("SELECT id, full_name FROM Workers WHERE is_active=1 ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);
$materials = $pdo->query("SELECT id, COALESCE(code||' - ','') || name AS label, unit FROM Materials ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

// Get existing actual resource usage
$actualWorkers = $pdo->prepare("SELECT a.date, w.full_name, a.hours, a.note
                     FROM WorkerAssignments a JOIN Workers w ON w.id=a.worker_id
                     WHERE a.request_id=? ORDER BY a.id DESC");
$actualWorkers->execute([$id]); 
$actual_worker_assignments = $actualWorkers->fetchAll(PDO::FETCH_ASSOC);

$actualMaterials = $pdo->prepare("SELECT m.name, COALESCE(m.code,'') AS code, u.quantity, u.unit, u.note
                     FROM MaterialUsage u JOIN Materials m ON m.id=u.material_id
                     WHERE u.request_id=? ORDER BY u.id DESC");
$actualMaterials->execute([$id]); 
$actual_material_usage = $actualMaterials->fetchAll(PDO::FETCH_ASSOC);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD']==='POST') {
    // Update request details
    if (isset($_POST['update_request'])) {
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $unit = trim($_POST['unit'] ?? '');
        $priority_id = (int)($_POST['priority_id'] ?? 2);
        $call_number = trim($_POST['call_number'] ?? '');
        $planned_start = trim($_POST['planned_start'] ?? '');
        $planned_finish = trim($_POST['planned_finish'] ?? '');
        
        if ($title !== '') {
            // First, get the current values to compare
            $stmt = $pdo->prepare("SELECT * FROM Requests WHERE id=?");
            $stmt->execute([$id]);
            $current_request = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stmt = $pdo->prepare("UPDATE Requests SET title=?, description=?, unit=?, priority_id=?, planned_start=?, planned_finish=?, call_number=? WHERE id=?");
            $stmt->execute([$title, $description, $unit, $priority_id, $planned_start, $planned_finish, $call_number, $id]);
            
            // Log the update with detailed information about what changed
            $changes = [];
            if ($title !== $current_request['title']) {
                $changes[] = "Başlık: " . $current_request['title'] . " → " . $title;
            }
            if ($description !== $current_request['description']) {
                $changes[] = "Açıklama: " . (empty($current_request['description']) ? '(Boş)' : substr($current_request['description'], 0, 30) . (strlen($current_request['description']) > 30 ? '...' : '')) . " → " . (empty($description) ? '(Boş)' : substr($description, 0, 30) . (strlen($description) > 30 ? '...' : ''));
            }
            if ($unit !== $current_request['unit']) {
                $changes[] = "Birim: " . (empty($current_request['unit']) ? '(Boş)' : $current_request['unit']) . " → " . (empty($unit) ? '(Boş)' : $unit);
            }
            if ($priority_id !== (int)$current_request['priority_id']) {
                $priority_names = [1 => 'Düşük', 2 => 'Orta', 3 => 'Yüksek', 4 => 'Acil'];
                $old_priority = $priority_names[(int)$current_request['priority_id']] ?? 'Bilinmiyor';
                $new_priority = $priority_names[$priority_id] ?? 'Bilinmiyor';
                $changes[] = "Öncelik: " . $old_priority . " → " . $new_priority;
            }
            if ($call_number !== ($current_request['call_number'] ?? '')) {
                $changes[] = "Çağrı No: " . (empty($current_request['call_number']) ? '(Boş)' : $current_request['call_number']) . " → " . (empty($call_number) ? '(Boş)' : $call_number);
            }
            if ($planned_start !== $current_request['planned_start']) {
                $changes[] = "Planlanan başlangıç: " . (empty($current_request['planned_start']) ? '(Boş)' : $current_request['planned_start']) . " → " . (empty($planned_start) ? '(Boş)' : $planned_start);
            }
            if ($planned_finish !== $current_request['planned_finish']) {
                $changes[] = "Planlanan bitiş: " . (empty($current_request['planned_finish']) ? '(Boş)' : $current_request['planned_finish']) . " → " . (empty($planned_finish) ? '(Boş)' : $planned_finish);
            }
            
            if (!empty($changes)) {
                $note = 'Talep detayları güncellendi (' . implode(", ", $changes) . ')';
            } else {
                $note = 'Talep detayları güncellendi (değişiklik yok)';
            }
            
            $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                           VALUES(?,?,?,?,?,?)")->execute([$id, $req['status_id'], $req['status_id'], $me['id'], now(), $note]);
        }
        redirect("/request_edit.php?id=$id");
    }
    
    // Add actual worker assignment
    if (isset($_POST['add_worker']) && $req['status_id'] < 6) { // Not completed yet
        $stmt=$pdo->prepare("INSERT INTO WorkerAssignments(request_id,worker_id,date,hours,note) VALUES(?,?,?,?,?)");
        $stmt->execute([$id,(int)$_POST['worker_id'],$_POST['date'],(float)$_POST['hours'],trim($_POST['note'] ?? '')]);
        redirect("/request_edit.php?id=$id#workers");
    }
    
    // Add actual material usage
    if (isset($_POST['add_material']) && $req['status_id'] < 6) { // Not completed yet
        $stmt=$pdo->prepare("INSERT INTO MaterialUsage(request_id,material_id,quantity,unit,note) VALUES(?,?,?,?,?)");
        $stmt->execute([$id,(int)$_POST['material_id'],(float)$_POST['quantity'],trim($_POST['unit']),trim($_POST['note'] ?? '')]);
        redirect("/request_edit.php?id=$id#materials");
    }
    
    // Mark as completed
    if (isset($_POST['complete']) && $req['status_id'] == 5) { // Only if in progress
        $actual_start = trim($_POST['actual_start'] ?? $req['planned_start']);
        $actual_finish = trim($_POST['actual_finish'] ?? $req['planned_finish']);
        $completion_note = trim($_POST['completion_note'] ?? '');
        
        $pdo->beginTransaction();
        try {
            // Update request status to pending completion approval
            $pdo->prepare("UPDATE Requests SET status_id=8, actual_start=?, actual_finish=? WHERE id=?")
                ->execute([$actual_start, $actual_finish, $id]);
            
            // Log the completion request with detailed information
            $note = 'İş tamamlama talebi oluşturuldu';
            if ($completion_note !== '') {
                $note .= ': ' . $completion_note;
            }
            
            // Add detailed information about what changed
            $changes = [];
            if ($actual_start !== ($req['actual_start'] ?? $req['planned_start'])) {
                $changes[] = "Gerçek başlangıç tarihi: " . ($req['actual_start'] ?? $req['planned_start'] ?? 'Belirlenmemiş') . " → " . $actual_start;
            }
            if ($actual_finish !== ($req['actual_finish'] ?? $req['planned_finish'])) {
                $changes[] = "Gerçek bitiş tarihi: " . ($req['actual_finish'] ?? $req['planned_finish'] ?? 'Belirlenmemiş') . " → " . $actual_finish;
            }
            
            if (!empty($changes)) {
                $note .= " (" . implode(", ", $changes) . ")";
            }
            
            $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                           VALUES(?,?,?,?,?,?)")->execute([$id, 5, 8, $me['id'], now(), $note]);
            
            $pdo->commit();
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
        redirect("/request_edit.php?id=$id");
    }
}

// Calculate totals for summary
$totalWorkerHours = 0;
foreach ($actual_worker_assignments as $assignment) {
    $totalWorkerHours += (float)$assignment['hours'];
}

$totalMaterialUsage = [];
foreach ($actual_material_usage as $usage) {
    $key = $usage['code'] . ' ' . $usage['name'] . ' (' . $usage['unit'] . ')';
    if (!isset($totalMaterialUsage[$key])) {
        $totalMaterialUsage[$key] = 0;
    }
    $totalMaterialUsage[$key] += (float)$usage['quantity'];
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>İş #<?=$id?> • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{--bg:#0b1220;--panel:#0f172a;--muted:#94a3b8;--text:#e2e8f0;--accent:#22c55e;--line:#1f2937;}
    *{box-sizing:border-box} body{margin:0;background:#0b1220;color:var(--text);font:15px/1.55 system-ui,Segoe UI,Arial}
    header{display:flex;justify-content:space-between;align-items:center;padding:18px 22px;border-bottom:1px solid var(--line);background:#0f172a;position:sticky;top:0}
    main{max-width:1200px;margin:18px auto;padding:0 22px}
    a{color:#93c5fd}
    .card{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:16px;margin:14px 0}
    h2{margin:0 0 12px;font-size:18px}
    label{display:block;margin:8px 0;color:#cbd5e1}
    input,select,textarea{width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px}
    textarea{min-height:90px}
    button{padding:10px 14px;border-radius:10px;border:0;background:var(--accent);color:#062813;font-weight:600;cursor:pointer}
    table{width:100%;border-collapse:collapse;margin-top:12px}
    th,td{border-bottom:1px solid #1f2937;padding:10px;text-align:left}
    tr:hover td{background:#0b1725}
    .row{display:flex;gap:10px;flex-wrap:wrap}
    .btns form{display:inline-block;margin-right:8px}
    .muted{color:#94a3b8}
    .summary-card{background:#1e293b;border:1px solid #334155;border-radius:10px;padding:16px;margin:16px 0}
  </style>
</head>
<body>
<header>
  <div class="row">
    <a href="/requests.php">← Talepler</a>
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
  </div>
  <div>#<?=$id?> • <?=e($req['title'])?></div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <section class="card">
    <h2>Talep Detayları</h2>
    <form method="post">
      <input type="hidden" name="update_request" value="1">
      <div class="row">
        <label style="flex:1 1 300px">Talep Başlığı
          <input name="title" value="<?=e($req['title'])?>" required>
        </label>
        <label style="flex:1 1 200px">Talep Çağrı No
          <input name="call_number" value="<?=e($req['call_number'] ?? '')?>">
        </label>
        <label style="flex:1 1 150px">Birim
          <input name="unit" value="<?=e($req['unit'])?>">
        </label>
        <label style="flex:1 1 150px">Öncelik
          <select name="priority_id">
            <option value="1" <?=($req['priority_id']==1?'selected':'')?>>Düşük</option>
            <option value="2" <?=($req['priority_id']==2?'selected':'')?>>Orta</option>
            <option value="3" <?=($req['priority_id']==3?'selected':'')?>>Yüksek</option>
            <option value="4" <?=($req['priority_id']==4?'selected':'')?>>Acil</option>
          </select>
        </label>
      </div>
      
      <div class="row">
        <label style="flex:1 1 200px">Planlanan Başlama Tarihi
          <input type="date" name="planned_start" value="<?=e($req['planned_start'])?>">
        </label>
        <label style="flex:1 1 200px">Planlanan Bitiş Tarihi
          <input type="date" name="planned_finish" value="<?=e($req['planned_finish'])?>">
        </label>
      </div>
      
      <label>Açıklama
        <textarea name="description" style="width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px;min-height:80px"><?=e($req['description'])?></textarea>
      </label>
      
      <button type="submit">Güncelle</button>
    </form>
  </section>
  
  <?php if($req['status_id'] == 5): // Only show completion form if in progress ?>
  <section class="card" id="completion">
    <h2>İşi Tamamla</h2>
    <form method="post" class="row">
      <input type="hidden" name="complete" value="1">
      <div class="row" style="width:100%">
        <label style="flex:1 1 200px">Gerçek Başlama Tarihi
          <input type="date" name="actual_start" value="<?=e($req['actual_start'] ?? $req['planned_start'])?>">
        </label>
        <label style="flex:1 1 200px">Gerçek Bitiş Tarihi
          <input type="date" name="actual_finish" value="<?=e($req['actual_finish'] ?? $req['planned_finish'])?>">
        </label>
      </div>
      <label style="width:100%">Tamamlama Notu (İsteğe Bağlı)
        <textarea name="completion_note" placeholder="Sapma varsa gerekçesini yazın..."></textarea>
      </label>
      <button type="submit" onclick="return confirm('İşi tamamlamak istediğinizden emin misiniz?')">Tamamlandı</button>
    </form>
  </section>
  <?php endif; ?>
  
  <section class="card" id="workers">
    <h2>Gerçek Adam/Saat Girişi</h2>
    <?php if($req['status_id'] < 6): // Not completed yet ?>
    <form method="post" class="row">
      <input type="hidden" name="add_worker" value="1">
      <label style="flex:1 1 160px">Tarih
        <input type="date" name="date" required value="<?=date('Y-m-d')?>">
      </label>
      <label style="flex:2 1 260px">Çalışan
        <select name="worker_id" required>
          <option value="">Çalışan seçin...</option>
          <?php foreach($workers as $w): ?>
            <option value="<?=$w['id']?>"><?=e($w['full_name'])?></option>
          <?php endforeach; ?>
        </select>
      </label>
      <label style="flex:1 1 120px">Saat
        <input type="number" step="0.25" min="0" name="hours" required>
      </label>
      <label style="flex:2 1 260px">Not
        <input name="note">
      </label>
      <button>Ekle</button>
    </form>
    <?php else: ?>
    <p class="muted">İş tamamlandığı için yeni kayıt eklenemez.</p>
    <?php endif; ?>
    
    <?php if($actual_worker_assignments): ?>
    <table>
      <tr><th>Tarih</th><th>Çalışan</th><th>Saat</th><th>Not</th></tr>
      <?php foreach($actual_worker_assignments as $r): ?>
      <tr>
        <td><?=e($r['date'])?></td>
        <td><?=e($r['full_name'])?></td>
        <td><?=e($r['hours'])?></td>
        <td><?=e($r['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <p class="muted">Henüz çalışan ataması yapılmamış.</p>
    <?php endif; ?>
  </section>
  
  <section class="card" id="materials">
    <h2>Gerçek Malzeme Kullanımı</h2>
    <?php if($req['status_id'] < 6): // Not completed yet ?>
    <form method="post" class="row">
      <input type="hidden" name="add_material" value="1">
      <label style="flex:2 1 260px">Malzeme
        <select name="material_id" required>
          <option value="">Malzeme seçin...</option>
          <?php foreach($materials as $m): ?>
            <option value="<?=$m['id']?>" data-unit="<?=e($m['unit'])?>"><?=e($m['label'])?></option>
          <?php endforeach; ?>
        </select>
      </label>
      <label style="flex:1 1 140px">Miktar
        <input type="number" step="0.01" min="0" name="quantity" required>
      </label>
      <label style="flex:1 1 120px">Birim
        <input name="unit" required>
      </label>
      <label style="flex:2 1 260px">Not
        <input name="note">
      </label>
      <button>Ekle</button>
    </form>
    <?php else: ?>
    <p class="muted">İş tamamlandığı için yeni kayıt eklenemez.</p>
    <?php endif; ?>
    
    <?php if($actual_material_usage): ?>
    <table>
      <tr><th>Kod</th><th>Malzeme</th><th>Miktar</th><th>Birim</th><th>Not</th></tr>
      <?php foreach($actual_material_usage as $r): ?>
      <tr>
        <td><?=e($r['code'])?></td>
        <td><?=e($r['name'])?></td>
        <td><?=e($r['quantity'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <p class="muted">Henüz malzeme kullanımı kaydedilmemiş.</p>
    <?php endif; ?>
  </section>
  
  <section class="card">
    <h2>Özet ve Kontrol</h2>
    <div class="summary-card">
      <h3>Toplam Harcanan Kaynaklar</h3>
      <p><b>Toplam Adam/Saat:</b> <?=$totalWorkerHours?> saat</p>
      
      <h4 style="margin-top:16px">Malzeme Kullanımı:</h4>
      <?php if($totalMaterialUsage): ?>
        <ul>
          <?php foreach($totalMaterialUsage as $item => $quantity): ?>
            <li><?=e($item)?>: <?=number_format($quantity, 2, ',', '.')?></li>
          <?php endforeach; ?>
        </ul>
      <?php else: ?>
        <p class="muted">Henüz malzeme kullanımı kaydedilmemiş.</p>
      <?php endif; ?>
    </div>
    
    <p class="muted">Yukarıdaki bilgileri kontrol edin. İş tamamlandığında "Tamamlandı" butonuna tıklayarak işi kapatın.</p>
  </section>
</main>

<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});

// Auto-fill unit when material is selected
document.querySelectorAll('select[name="material_id"]').forEach(select => {
    select.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const unit = selectedOption.getAttribute('data-unit');
        const row = this.closest('.row');
        const unitInput = row.querySelector('input[name="unit"]');
        if (unitInput && unit) {
            unitInput.value = unit;
        }
    });
});
</script>
</body>
</html>