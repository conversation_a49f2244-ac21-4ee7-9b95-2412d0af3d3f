<?php
// C:\workapp\app\public\requests.php (<PERSON><PERSON> Listesi)
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';
require_login();
$me = current_user();

// Filtreler
$status_filter = (int)($_GET['status'] ?? 0);
$search_term = trim($_GET['search'] ?? '');

// Yönetici onayında bekleyen işler (sadece yöneticiler için)
$pending_approval = [];
if ($me['role'] === 'Manager') {
    $stmt = $pdo->prepare("SELECT r.id, r.title, r.unit, r.created_at, s.label status_label, u.display_name creator_name
                           FROM Requests r
                           JOIN Status s ON s.id=r.status_id
                           JOIN Users u ON u.id=r.created_by
                           WHERE r.status_id=3
                           ORDER BY r.created_at DESC LIMIT 10");
    $stmt->execute();
    $pending_approval = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Devam etmekte olan işler (sadece mühendisler için)
$ongoing_works = [];
if ($me['role'] === 'Engineer') {
    $stmt = $pdo->prepare("SELECT r.id, r.title, r.unit, r.created_at, s.label status_label, u.display_name creator_name
                           FROM Requests r
                           JOIN Status s ON s.id=r.status_id
                           JOIN Users u ON u.id=r.created_by
                           WHERE r.status_id IN (4, 5) AND (r.assigned_engineer = ? OR r.created_by = ?)
                           ORDER BY r.created_at DESC LIMIT 10");
    $stmt->execute([$me['id'], $me['id']]);
    $ongoing_works = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Ana talep listesi (son 300 kayıt) - kullanıcıya özel filtreleme
$sql = "SELECT r.id, r.title, r.unit, r.created_at, r.priority_id, s.label status_label, u.display_name creator_name,
               (SELECT display_name FROM Users WHERE id=r.assigned_engineer) AS assigned_engineer_name
        FROM Requests r
        JOIN Status s ON s.id=r.status_id
        JOIN Users u ON u.id=r.created_by";

$params = [];
$where_clauses = [];

// Kullanıcıya göre filtreleme
if ($me['role'] === 'Engineer') {
    // Mühendisler sadece kendi oluşturduğu ve kendisine atanan talepleri görebilir
    $where_clauses[] = "(r.created_by = ? OR r.assigned_engineer = ?)";
    $params[] = $me['id'];
    $params[] = $me['id'];
} elseif ($me['role'] === 'Manager') {
    // Yöneticiler kendi birimlerindeki talepleri görebilir
    if (!empty($me['unit'])) {
        $where_clauses[] = "(r.unit = ? OR r.created_by = ?)";
        $params[] = $me['unit'];
        $params[] = $me['id'];
    }
} 
// Admin tüm talepleri görebilir, ekstra filtreleme gerekmez

// Durum filtresi
if ($status_filter > 0) {
    $where_clauses[] = "r.status_id = ?";
    $params[] = $status_filter;
}

// Arama terimi
if ($search_term !== '') {
    $where_clauses[] = "(r.title LIKE ? OR r.description LIKE ?)";
    $params[] = "%$search_term%";
    $params[] = "%$search_term%";
}

if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}

$sql .= " ORDER BY r.created_at DESC LIMIT 300";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Durumlar
$statuses = $pdo->query("SELECT id, label FROM Status ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);

// Get workers and materials for the form
$workers = $pdo->query("SELECT id, full_name FROM Workers WHERE is_active=1 ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);
$materials = $pdo->query("SELECT id, code, name, unit FROM Materials WHERE name NOT LIKE '[SİLİNDİ] %' ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

// POST işlemleri (hızlı yeni talep)
if ($_SERVER['REQUEST_METHOD']==='POST' && isset($_POST['quick_add'])) {
    $title = trim($_POST['title'] ?? '');
    if ($title !== '') {
        $description = trim($_POST['description'] ?? '');
        $unit = trim($_POST['unit'] ?? $me['unit'] ?? '');
        $priority_id = (int)($_POST['priority_id'] ?? 2);
        $call_number = trim($_POST['call_number'] ?? '');
        $planned_start = trim($_POST['planned_start'] ?? '');
        $planned_finish = trim($_POST['planned_finish'] ?? '');
        
        // Get worker and material data
        $worker_ids = $_POST['workers'] ?? [];
        $worker_hours = $_POST['worker_hours'] ?? [];
        $worker_notes = $_POST['worker_notes'] ?? [];
        
        $material_ids = $_POST['materials'] ?? [];
        $material_quantities = $_POST['material_quantities'] ?? [];
        $material_units = $_POST['material_units'] ?? [];
        $material_notes = $_POST['material_notes'] ?? [];
        
        $pdo->beginTransaction();
        try {
            $stmt = $pdo->prepare("INSERT INTO Requests(created_at, created_by, title, description, unit, priority_id, status_id, planned_start, planned_finish, call_number)
                                   VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([now(), $me['id'], $title, $description, $unit, $priority_id, 1, $planned_start, $planned_finish, $call_number]); // status_id=1 (NEW)
            $new_id = $pdo->lastInsertId();
            
            // Log ekle
            $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                           VALUES(?,?,?,?,?,?)")->execute([$new_id, null, 1, $me['id'], now(), 'Yeni talep oluşturuldu']);
            
            // Store worker requirements in RequestResources table
            if (!empty($worker_ids)) {
                $workerStmt = $pdo->prepare("INSERT INTO RequestResources(request_id, worker_id, estimated_hours, note) VALUES(?, ?, ?, ?)");
                for ($i = 0; $i < count($worker_ids); $i++) {
                    if (!empty($worker_ids[$i])) {
                        $hours = $worker_hours[$i] ?? 0;
                        $note = $worker_notes[$i] ?? '';
                        $workerStmt->execute([$new_id, $worker_ids[$i], $hours, $note]);
                    }
                }
            }
            
            // Store material requirements in RequestResources table
            if (!empty($material_ids)) {
                $materialStmt = $pdo->prepare("INSERT INTO RequestResources(request_id, material_id, estimated_quantity, unit, note) VALUES(?, ?, ?, ?, ?)");
                for ($i = 0; $i < count($material_ids); $i++) {
                    if (!empty($material_ids[$i])) {
                        $quantity = $material_quantities[$i] ?? 0;
                        $unit = $material_units[$i] ?? '';
                        $note = $material_notes[$i] ?? '';
                        $materialStmt->execute([$new_id, $material_ids[$i], $quantity, $unit, $note]);
                    }
                }
            }
            
            $pdo->commit();
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
    }
    redirect('/requests.php');
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Talepler • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    :root{
      --bg-primary: #0f172a;
      --bg-secondary: #1e293b;
      --bg-tertiary: #334155;
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-muted: #64748b;
      --accent-primary: #3b82f6;
      --accent-success: #10b981;
      --accent-warning: #f59e0b;
      --accent-danger: #ef4444;
      --border: #475569;
      --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
      --gradient-primary: linear-gradient(135deg, var(--accent-primary), #2563eb);
      --gradient-success: linear-gradient(135deg, var(--accent-success), #059669);
    }

    * { box-sizing: border-box; }

    body {
      margin: 0;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      color: var(--text-primary);
      font: 15px/1.6 'Segoe UI', system-ui, -apple-system, sans-serif;
      min-height: 100vh;
    }

    header {
      background: rgba(30, 41, 59, 0.95);
      backdrop-filter: blur(20px) saturate(180%);
      border-bottom: 1px solid var(--border);
      padding: 1.5rem 2rem;
      position: sticky;
      top: 0;
      z-index: 100;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .header-nav {
      display: flex;
      gap: 2rem;
      align-items: center;
    }

    .header-nav a {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.2s ease;
      padding: 0.5rem 1rem;
      border-radius: 8px;
    }

    .header-nav a:hover {
      color: var(--accent-primary);
      background: rgba(59, 130, 246, 0.1);
    }

    .header-title {
      font-size: 1.5rem;
      font-weight: 700;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header-user {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .user-info {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    .logout-btn {
      color: var(--accent-danger);
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .logout-btn:hover {
      background: rgba(239, 68, 68, 0.1);
    }

    main {
      max-width: 1400px;
      margin: 2rem auto;
      padding: 0 2rem;
    }

    .card {
      background: var(--bg-secondary);
      border: 1px solid var(--border);
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow);
      position: relative;
      overflow: hidden;
    }

    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
      opacity: 0.5;
    }

    h2 {
      margin: 0 0 1.5rem;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    label {
      display: block;
      margin: 1rem 0 0.5rem;
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.95rem;
    }

    input, select, textarea {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 1px solid var(--border);
      background: var(--bg-primary);
      color: var(--text-primary);
      border-radius: 12px;
      font-size: 0.95rem;
      transition: all 0.2s ease;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    button {
      padding: 0.75rem 1.5rem;
      border-radius: 12px;
      border: none;
      background: var(--gradient-success);
      color: white;
      font-weight: 600;
      font-size: 0.95rem;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    }

    .btn-primary {
      background: var(--gradient-primary);
    }

    .btn-primary:hover {
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--accent-danger), #dc2626);
    }

    .btn-danger:hover {
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid var(--border);
    }

    th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
    }

    th {
      background: var(--bg-tertiary);
      font-weight: 600;
      color: var(--text-primary);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    td {
      color: var(--text-secondary);
    }

    tr:hover td {
      background: rgba(59, 130, 246, 0.05);
    }

    .row {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      align-items: end;
    }

    .filters {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      align-items: end;
      margin-bottom: 1.5rem;
      padding: 1.5rem;
      background: var(--bg-primary);
      border-radius: 12px;
      border: 1px solid var(--border);
    }

    .filter-item {
      flex: 1 1 200px;
      min-width: 200px;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 6px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .status-new { background: rgba(59, 130, 246, 0.2); color: var(--accent-primary); }
    .status-approved { background: rgba(16, 185, 129, 0.2); color: var(--accent-success); }
    .status-in-progress { background: rgba(245, 158, 11, 0.2); color: var(--accent-warning); }
    .status-completed { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
    .status-rejected { background: rgba(239, 68, 68, 0.2); color: var(--accent-danger); }

    .priority-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 6px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .priority-low { background: rgba(100, 116, 139, 0.2); color: var(--text-muted); }
    .priority-medium { background: rgba(245, 158, 11, 0.2); color: var(--accent-warning); }
    .priority-high { background: rgba(251, 146, 60, 0.2); color: #fb923c; }
    .priority-urgent { background: rgba(239, 68, 68, 0.2); color: var(--accent-danger); }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: var(--bg-primary);
      border: 1px solid var(--border);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.2s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: var(--accent-primary);
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .toggle-header {
      cursor: pointer;
      user-select: none;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .toggle-header:hover {
      color: var(--accent-primary);
    }

    .toggle-icon {
      transition: transform 0.2s ease;
      font-size: 1.2rem;
    }

    .toggle-icon.rotated {
      transform: rotate(180deg);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }

      .header-nav {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
      }

      .header-title {
        text-align: center;
        order: -1;
      }

      .header-user {
        justify-content: center;
      }

      main {
        padding: 1rem;
      }

      .card {
        padding: 1.5rem;
      }

      .row {
        flex-direction: column;
        gap: 1rem;
      }

      .filters {
        flex-direction: column;
        gap: 1rem;
      }

      .filter-item {
        min-width: auto;
      }

      table {
        font-size: 0.85rem;
      }

      th, td {
        padding: 0.75rem 0.5rem;
      }

      .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
      }

      .stat-card {
        padding: 1rem;
      }

      .stat-number {
        font-size: 1.5rem;
      }
    }

    @media (max-width: 480px) {
      .header-nav a {
        padding: 0.5rem;
        font-size: 0.85rem;
      }

      .card {
        padding: 1rem;
        margin-bottom: 1rem;
      }

      h2 {
        font-size: 1.25rem;
      }

      button {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
      }

      .quick-stats {
        grid-template-columns: 1fr 1fr;
      }
    }
    .worker-row, .material-row{align-items:end}
    
    /* Modern resource requirements styling */
    .resource-section { width:100%;border-top:1px solid var(--line);margin:16px 0;padding-top:16px }
    .resource-header { display:flex;justify-content:space-between;align-items:center;margin-bottom:16px }
    .resource-title { margin:0 }
    .resource-actions { display:flex;gap:8px }
    .resource-row { display:flex;gap:10px;margin-bottom:8px;align-items:end }
    .resource-field { flex:1 }
    .resource-field.worker-select { flex:2 }
    .resource-field.material-select { flex:2 }
    .resource-field.hours-input, .resource-field.quantity-input { flex:1 }
    .resource-field.unit-input { flex:1 }
    .resource-field.note-input { flex:2 }
    .resource-buttons { display:flex;gap:5px }
    .add-btn, .remove-btn { padding:8px 12px;font-size:14px }
    .remove-btn { background:#ef4444 }
  </style>
</head>
<body>
<header>
  <div class="header-content">
    <div class="header-nav">
      <?php if(in_array($me['role'],['Admin','Manager'])): ?>
      <a href="/reports.php">📊 Raporlar</a>
      <a href="/workers.php">👷 İşçiler</a>
      <a href="/materials.php">🔧 Malzemeler</a>
      <a href="/import_personnel.php">👥 Personel</a>
      <a href="/user_credentials.php">🔑 Giriş Bilgileri</a>
      <?php endif; ?>
    </div>
    <div class="header-title">📋 Talepler</div>
    <div class="header-user">
      <span class="user-info">👋 <?=e($me['name'])?></span>
      <a href="/logout.php" class="logout-btn">🚪 Çıkış</a>
    </div>
  </div>
</header>

<main>
  <!-- Quick Stats -->
  <div class="quick-stats">
    <div class="stat-card">
      <div class="stat-number"><?= count($requests) ?></div>
      <div class="stat-label">Toplam Talep</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><?= count(array_filter($requests, fn($r) => $r['status_id'] == 3)) ?></div>
      <div class="stat-label">Onay Bekleyen</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><?= count(array_filter($requests, fn($r) => $r['status_id'] == 5)) ?></div>
      <div class="stat-label">Devam Eden</div>
    </div>
    <div class="stat-card">
      <div class="stat-number"><?= count(array_filter($requests, fn($r) => $r['status_id'] == 6)) ?></div>
      <div class="stat-label">Tamamlanan</div>
    </div>
  </div>

  <?php if(in_array($me['role'],['Engineer','Manager','Admin'])): ?>
  <section class="card">
    <h2 class="toggle-header" onclick="toggleNewRequestForm()">
      🆕 Yeni Talep Oluştur
      <span id="toggle-icon" class="toggle-icon">▼</span>
    </h2>
    <div id="new-request-form" style="<?= $me['role'] === 'Manager' ? 'display: none;' : 'display: block;' ?>"
      <form method="post" class="row">
        <input type="hidden" name="quick_add" value="1">
        <div class="row" style="width:100%">
          <label style="flex:1 1 300px">Talep Başlığı
            <input name="title" placeholder="Kısa açıklama..." required>
          </label>
          <label style="flex:1 1 200px">Talep Çağrı No
            <input name="call_number" placeholder="Çağrı numarası...">
          </label>
          <label style="flex:1 1 150px">Öncelik
            <select name="priority_id">
              <option value="1">Düşük</option>
              <option value="2" selected>Orta</option>
              <option value="3">Yüksek</option>
              <option value="4">Acil</option>
            </select>
          </label>
        </div>
        <div class="row" style="width:100%">
          <label style="flex:1 1 200px">Planlanan Başlama Tarihi
            <input type="date" name="planned_start" value="<?=date('Y-m-d')?>">
          </label>
          <label style="flex:1 1 200px">Planlanan Bitiş Tarihi
            <input type="date" name="planned_finish" value="<?=date('Y-m-d', strtotime('+7 days'))?>">
          </label>
        </div>
        <label style="width:100%">Açıklama
          <textarea name="description" placeholder="Detaylı açıklama..." style="width:100%;padding:10px 12px;border:1px solid #223047;background:#0b1220;color:var(--text);border-radius:10px;min-height:80px"></textarea>
        </label>
        
        <div class="resource-section">
          <div class="resource-header">
            <h3 class="resource-title">Tahmini Kaynak Gereksinimleri</h3>
          </div>
          
          <div class="resource-section">
            <div class="resource-header">
              <h4>Gerekli Çalışanlar</h4>
              <div class="resource-actions">
                <button type="button" class="add-btn" onclick="addWorkerRow()">+ Ekle</button>
              </div>
            </div>
            <div id="workers-container">
              <div class="resource-row worker-row">
                <select name="workers[]" class="resource-field worker-select">
                  <option value="">Çalışan seçin...</option>
                  <?php foreach($workers as $worker): ?>
                    <option value="<?=$worker['id']?>"><?=e($worker['full_name'])?></option>
                  <?php endforeach; ?>
                </select>
                <input type="number" name="worker_hours[]" placeholder="Saat" step="0.25" min="0" class="resource-field hours-input">
                <input type="text" name="worker_notes[]" placeholder="Not" class="resource-field note-input">
                <div class="resource-buttons">
                  <button type="button" class="remove-btn" onclick="removeWorkerRow(this)">- Sil</button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="resource-section">
            <div class="resource-header">
              <h4>Gerekli Malzemeler</h4>
              <div class="resource-actions">
                <button type="button" class="add-btn" onclick="addMaterialRow()">+ Ekle</button>
              </div>
            </div>
            <div id="materials-container">
              <div class="resource-row material-row">
                <select name="materials[]" class="resource-field material-select" onchange="updateMaterialUnit(this)">
                  <option value="">Malzeme seçin...</option>
                  <?php foreach($materials as $material): ?>
                    <option value="<?=$material['id']?>" data-unit="<?=e($material['unit'])?>"><?=e($material['code'] ? $material['code'].' - ' : '').$material['name']?></option>
                  <?php endforeach; ?>
                </select>
                <input type="number" name="material_quantities[]" placeholder="Miktar" step="0.01" min="0" class="resource-field quantity-input">
                <input type="text" name="material_units[]" placeholder="Birim" class="resource-field unit-input">
                <input type="text" name="material_notes[]" placeholder="Not" class="resource-field note-input">
                <div class="resource-buttons">
                  <button type="button" class="remove-btn" onclick="removeMaterialRow(this)">- Sil</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <button style="align-self:flex-end">Talep Oluştur</button>
      </form>
    </div>
  </section>
  <?php endif; ?>

  <?php if($me['role'] === 'Manager' && $pending_approval): ?>
  <section class="card">
    <h2>Yönetici Onayında Bekleyen İşler</h2>
    <table>
      <tr>
        <th>ID</th>
        <th>Başlık</th>
        <th>Birim</th>
        <th>Oluşturan</th>
        <th>Tarih</th>
        <th>Durum</th>
        <th>İşlem</th>
      </tr>
      <?php foreach($pending_approval as $r): ?>
      <tr>
        <td><a href="/request_view.php?id=<?=$r['id']?>">#<?=$r['id']?></a></td>
        <td><?=e($r['title'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['creator_name'])?></td>
        <td><?=e(substr($r['created_at'],0,16))?></td>
        <td><?=e($r['status_label'])?></td>
        <td><a href="/request_view.php?id=<?=$r['id']?>">Aç</a></td>
      </tr>
      <?php endforeach; ?>
    </table>
  </section>
  <?php endif; ?>

  <?php if($me['role'] === 'Engineer' && $ongoing_works): ?>
  <section class="card">
    <h2>Devam Etmekte Olan İşler</h2>
    <table>
      <tr>
        <th>ID</th>
        <th>Başlık</th>
        <th>Birim</th>
        <th>Oluşturan</th>
        <th>Tarih</th>
        <th>Durum</th>
        <th>İşlem</th>
      </tr>
      <?php foreach($ongoing_works as $r): ?>
      <tr>
        <td><a href="/request_view.php?id=<?=$r['id']?>">#<?=$r['id']?></a></td>
        <td><?=e($r['title'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['creator_name'])?></td>
        <td><?=e(substr($r['created_at'],0,16))?></td>
        <td><?=e($r['status_label'])?></td>
        <td>
          <a href="/request_view.php?id=<?=$r['id']?>">Aç</a>
          <?php if($r['status_id'] == 5): ?>
            | <a href="/request_edit.php?id=<?=$r['id']?>">Düzenle</a>
          <?php endif; ?>
        </td>
      </tr>
      <?php endforeach; ?>
    </table>
  </section>
  <?php endif; ?>

  <section class="card">
    <div class="row" style="justify-content:space-between;align-items:center">
      <h2>Talepler</h2>
      <div>
        <?php if(in_array($me['role'], ['Admin', 'Manager'])): ?>
          <a href="/export_requests.php" style="display:inline-block;padding:8px 12px;background:#0ea5e9;color:white;border-radius:8px;text-decoration:none;margin-right:10px">
            CSV Dışa Aktar
          </a>
          <a href="/export_detailed.php" style="display:inline-block;padding:8px 12px;background:#8b5cf6;color:white;border-radius:8px;text-decoration:none;margin-right:10px">
            Detaylı Dışa Aktar
          </a>
          <?php if($me['role'] === 'Admin'): ?>
            <a href="/import_requests.php" style="display:inline-block;padding:8px 12px;background:#f59e0b;color:white;border-radius:8px;text-decoration:none">
              CSV İçe Aktar
            </a>
          <?php endif; ?>
        <?php endif; ?>
      </div>
    </div>
    
    <div class="row" style="margin-bottom:16px">
      <form method="get" class="filters">
        <div class="filter-item">
          <label>Durum
            <select name="status">
              <option value="0">Tümü</option>
              <?php foreach($statuses as $s): ?>
                <option value="<?=$s['id']?>" <?=($status_filter==$s['id'])?'selected':''?>><?=e($s['label'])?></option>
              <?php endforeach; ?>
            </select>
          </label>
        </div>
        <div class="filter-item">
          <label>Arama
            <input name="search" value="<?=e($search_term)?>" placeholder="Başlık veya açıklama...">
          </label>
        </div>
        <div class="filter-item">
          <button type="submit">Filtrele</button>
        </div>
      </form>

    <?php if($requests): ?>
    <table>
      <tr>
        <th>ID</th>
        <th>Başlık</th>
        <th>Birim</th>
        <th>Öncelik</th>
        <th>Atanan</th>
        <th>Oluşturan</th>
        <th>Tarih</th>
        <th>Durum</th>
        <th>İşlem</th>
      </tr>
      <?php foreach($requests as $r): ?>
      <tr>
        <td><a href="/request_view.php?id=<?=$r['id']?>">#<?=$r['id']?></a></td>
        <td><?=e($r['title'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['priority_id']==1?'Düşük':($r['priority_id']==2?'Orta':($r['priority_id']==3?'Yüksek':'Acil')))?></td>
        <td><?=e($r['assigned_engineer_name'] ?? '-')?></td>
        <td><?=e($r['creator_name'])?></td>
        <td><?=e(substr($r['created_at'],0,16))?></td>
        <td><?=e($r['status_label'])?></td>
        <td>
          <a href="/request_view.php?id=<?=$r['id']?>">Aç</a>
          <?php if($me['role']==='Engineer' && $r['status_id']==5): ?>
            | <a href="/request_edit.php?id=<?=$r['id']?>">Düzenle</a>
          <?php endif; ?>
        </td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <p class="muted">Kayıt bulunamadı.</p>
    <?php endif; ?>
  </section>
</main>

<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});

function toggleNewRequestForm() {
    const form = document.getElementById('new-request-form');
    const icon = document.getElementById('toggle-icon');
    
    if (form.style.display === 'none') {
        form.style.display = 'block';
        icon.textContent = '▲';
    } else {
        form.style.display = 'none';
        icon.textContent = '▼';
    }
}

function addWorkerRow() {
    const container = document.getElementById('workers-container');
    const workerRow = document.createElement('div');
    workerRow.className = 'resource-row worker-row';
    workerRow.innerHTML = `
      <select name="workers[]" class="resource-field worker-select">
        <option value="">Çalışan seçin...</option>
        <?php foreach($workers as $worker): ?>
          <option value="<?=$worker['id']?>"><?=e($worker['full_name'])?></option>
        <?php endforeach; ?>
      </select>
      <input type="number" name="worker_hours[]" placeholder="Saat" step="0.25" min="0" class="resource-field hours-input">
      <input type="text" name="worker_notes[]" placeholder="Not" class="resource-field note-input">
      <div class="resource-buttons">
        <button type="button" class="remove-btn" onclick="removeWorkerRow(this)">- Sil</button>
      </div>
    `;
    container.appendChild(workerRow);
}

function removeWorkerRow(button) {
    const row = button.closest('.worker-row');
    if (document.querySelectorAll('.worker-row').length > 1) {
        row.remove();
    } else {
        // Clear the fields instead of removing the last row
        row.querySelector('select').value = '';
        row.querySelector('input[name="worker_hours[]"]').value = '';
        row.querySelector('input[name="worker_notes[]"]').value = '';
    }
}

function addMaterialRow() {
    const container = document.getElementById('materials-container');
    const materialRow = document.createElement('div');
    materialRow.className = 'resource-row material-row';
    materialRow.innerHTML = `
      <select name="materials[]" class="resource-field material-select" onchange="updateMaterialUnit(this)">
        <option value="">Malzeme seçin...</option>
        <?php foreach($materials as $material): ?>
          <option value="<?=$material['id']?>" data-unit="<?=e($material['unit'])?>"><?=e($material['code'] ? $material['code'].' - ' : '').$material['name']?></option>
        <?php endforeach; ?>
      </select>
      <input type="number" name="material_quantities[]" placeholder="Miktar" step="0.01" min="0" class="resource-field quantity-input">
      <input type="text" name="material_units[]" placeholder="Birim" class="resource-field unit-input">
      <input type="text" name="material_notes[]" placeholder="Not" class="resource-field note-input">
      <div class="resource-buttons">
        <button type="button" class="remove-btn" onclick="removeMaterialRow(this)">- Sil</button>
      </div>
    `;
    container.appendChild(materialRow);
}

function removeMaterialRow(button) {
    const row = button.closest('.material-row');
    if (document.querySelectorAll('.material-row').length > 1) {
        row.remove();
    } else {
        // Clear the fields instead of removing the last row
        row.querySelector('select').value = '';
        row.querySelector('input[name="material_quantities[]"]').value = '';
        row.querySelector('input[name="material_units[]"]').value = '';
        row.querySelector('input[name="material_notes[]"]').value = '';
    }
}

function updateMaterialUnit(select) {
    const selectedOption = select.options[select.selectedIndex];
    const unit = selectedOption.getAttribute('data-unit');
    const row = select.closest('.material-row');
    const unitInput = row.querySelector('input[name="material_units[]"]');
    if (unitInput && unit) {
        unitInput.value = unit;
    }
}

// Sorting functionality
function sortTable(table, columnIndex, dataType) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.getAttribute('data-sort-order') !== 'asc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        let comparison = 0;
        
        if (dataType === 'number') {
            const aNum = parseFloat(aText) || 0;
            const bNum = parseFloat(bText) || 0;
            comparison = aNum - bNum;
        } else {
            comparison = aText.localeCompare(bText, 'tr', { numeric: true, sensitivity: 'base' });
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Toggle sort order
    table.setAttribute('data-sort-order', isAscending ? 'asc' : 'desc');
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update header indicators
    const headers = table.querySelectorAll('th');
    headers.forEach((header, index) => {
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
            if (index === columnIndex) {
                indicator.textContent = isAscending ? ' ↑' : ' ↓';
            } else {
                indicator.textContent = '';
            }
        }
    });
}

// Add double-click sorting to all table headers
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            // Add sort indicator
            const indicator = document.createElement('span');
            indicator.className = 'sort-indicator';
            indicator.style.marginLeft = '5px';
            header.appendChild(indicator);
            
            // Determine data type (simple heuristic)
            const firstRow = table.querySelector('tbody tr');
            let dataType = 'text';
            if (firstRow) {
                const cellText = firstRow.cells[index].textContent.trim();
                if (!isNaN(cellText) && cellText !== '') {
                    dataType = 'number';
                }
            }
            
            // Add double-click event
            header.addEventListener('dblclick', () => {
                sortTable(table, index, dataType);
            });
        });
    });
});
</script>
</body>
</html>