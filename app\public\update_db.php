<?php
// Simple script to update the database with the new status
require __DIR__.'/../inc/db.php';

try {
    // Check if the status already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM Status WHERE id = 8");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert the new status
        $pdo->prepare("INSERT INTO Status(id,code,label) VALUES(8,'PENDING_COMPLETION','Tamam<PERSON>a Onayı Bekliyor')")->execute();
        echo "<h2>Database Update Successful</h2>";
        echo "<p>New status 'PENDING_COMPLETION' has been added to the database.</p>";
    } else {
        echo "<h2>Status Already Exists</h2>";
        echo "<p>The 'PENDING_COMPLETION' status is already in the database.</p>";
    }
    
    // Display all statuses
    echo "<h3>Current Statuses in Database:</h3>";
    $stmt = $pdo->query("SELECT * FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Code</th><th>Label</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr><td>" . $status['id'] . "</td><td>" . $status['code'] . "</td><td>" . $status['label'] . "</td></tr>";
    }
    echo "</table>";
    
    echo "<p><a href='/requests.php'>Return to main application</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error</h2>";
    echo "<p>Could not update database: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='/requests.php'>Return to main application</a></p>";
}
?>