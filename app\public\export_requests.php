<?php
// Export all requests to CSV
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
$me = current_user();

// Only allow Ad<PERSON> and Manager roles to export
if (!in_array($me['role'], ['Admin', 'Manager'])) {
    http_response_code(403);
    echo "Bu işlem için yetkiniz yok.";
    exit;
}

// Get all requests with detailed information
$stmt = $pdo->prepare("SELECT 
    r.id,
    r.created_at,
    r.title,
    r.description,
    r.unit,
    r.call_number,
    p.name as priority,
    s.label as status,
    u.display_name as creator,
    eng.display_name as assigned_engineer,
    mgr.display_name as manager,
    r.approved_at,
    r.planned_start,
    r.planned_finish,
    r.actual_start,
    r.actual_finish
FROM Requests r
LEFT JOIN Priorities p ON r.priority_id = p.id
LEFT JOIN Status s ON r.status_id = s.id
LEFT JOIN Users u ON r.created_by = u.id
LEFT JOIN Users eng ON r.assigned_engineer = eng.id
LEFT JOIN Users mgr ON r.manager_id = mgr.id
ORDER BY r.id");

$stmt->execute();
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename=requests_export_' . date('Y-m-d_H-i-s') . '.csv');

// Open output stream
$output = fopen('php://output', 'w');

// Add BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write CSV header
fputcsv($output, [
    'ID',
    'Oluşturulma Tarihi',
    'Başlık',
    'Açıklama',
    'Birim',
    'Çağrı Numarası',
    'Öncelik',
    'Durum',
    'Talep Sahibi',
    'Atanan Mühendis',
    'Onaylayan Yönetici',
    'Onay Tarihi',
    'Planlanan Başlangıç',
    'Planlanan Bitiş',
    'Gerçek Başlangıç',
    'Gerçek Bitiş'
]);

// Write data rows
foreach ($requests as $request) {
    fputcsv($output, [
        $request['id'],
        $request['created_at'],
        $request['title'],
        $request['description'],
        $request['unit'],
        $request['call_number'],
        $request['priority'],
        $request['status'],
        $request['creator'],
        $request['assigned_engineer'],
        $request['manager'],
        $request['approved_at'],
        $request['planned_start'],
        $request['planned_finish'],
        $request['actual_start'],
        $request['actual_finish']
    ]);
}

fclose($output);
exit;
?>