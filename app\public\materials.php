<?php
// C:\workapp\app\public\materials.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';

require_login();
// Only Engineers can manage materials
require_role(['Admin', 'Manager', 'Engineer']);

// Get disciplines for dropdown
$disciplines = $pdo->query("SELECT * FROM Disciplines ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_material']) && in_array($me['role'], ['Engineer', 'Manager'])) {
        $code = trim($_POST['code'] ?? '');
        $name = trim($_POST['name'] ?? '');
        $unit = trim($_POST['unit'] ?? '');
        $discipline = trim($_POST['discipline'] ?? '');
        
        if ($name !== '' && $unit !== '' && $discipline !== '') {
            if ($me['role'] === 'Engineer') {
                // Engineers need manager approval
                // For now, we'll just add it directly but in a real system this would go through approval
                $stmt = $pdo->prepare("INSERT INTO Materials(code, name, unit, discipline) VALUES(?, ?, ?, ?)");
                $stmt->execute([$code, $name, $unit, $discipline]);
            } else {
                // Managers can add directly
                $stmt = $pdo->prepare("INSERT INTO Materials(code, name, unit, discipline) VALUES(?, ?, ?, ?)");
                $stmt->execute([$code, $name, $unit, $discipline]);
            }
        }
        redirect('/materials.php');
    }
    
    if (isset($_POST['update_material']) && in_array($me['role'], ['Engineer', 'Manager'])) {
        $id = (int)($_POST['id'] ?? 0);
        $code = trim($_POST['code'] ?? '');
        $name = trim($_POST['name'] ?? '');
        $unit = trim($_POST['unit'] ?? '');
        $discipline = trim($_POST['discipline'] ?? '');
        
        if ($id > 0 && $name !== '' && $unit !== '' && $discipline !== '') {
            $stmt = $pdo->prepare("UPDATE Materials SET code=?, name=?, unit=?, discipline=? WHERE id=?");
            $stmt->execute([$code, $name, $unit, $discipline, $id]);
        }
        redirect('/materials.php');
    }
    
    if (isset($_POST['delete_material']) && in_array($me['role'], ['Engineer', 'Manager'])) {
        $id = (int)($_POST['id'] ?? 0);
        if ($id > 0) {
            // Instead of deleting, we'll just mark as inactive by setting name to '[SİLİNDİ]'
            $stmt = $pdo->prepare("UPDATE Materials SET name='[SİLİNDİ] ' || name WHERE id=?");
            $stmt->execute([$id]);
        }
        redirect('/materials.php');
    }
}

// Get filter parameters
$discipline_filter = $_GET['discipline'] ?? '';

// Build query with discipline filter
$materialsQuery = "SELECT * FROM Materials WHERE name NOT LIKE '[SİLİNDİ] %'";
$params = [];

if ($discipline_filter) {
    $materialsQuery .= " AND discipline = ?";
    $params[] = $discipline_filter;
}

$materialsQuery .= " ORDER BY discipline, name";

$stmt = $pdo->prepare($materialsQuery);
$stmt->execute($params);
$materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>Malzemeler • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="/assets/css/workapp-modern.css">
    .filters{display:flex;gap:12px;flex-wrap:wrap;align-items:end;margin-bottom:16px}
    .filter-item{flex:1 1 200px}
    .modal{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);z-index:1000;align-items:center;justify-content:center}
    .modal-content{background:#0f172a;border:1px solid var(--line);border-radius:14px;padding:20px;max-width:500px;width:90%}
    .close{float:right;font-size:24px;cursor:pointer}
  </style>
</head>
<body>
<header>
  <div class="row">
    <?php if(in_array($me['role'],['Admin','Manager'])): ?>
    <a href="/reports.php">Raporlar</a>
    <?php endif; ?>
    <a href="/requests.php">Talepler</a>
    <a href="/workers.php">Çalışanlar</a>
    <a href="/materials.php">Malzemeler</a>
  </div>
  <div>Malzemeler</div>
  <div>
    <a href="/logout.php">Çıkış (<?=e($me['name'])?>)</a>
  </div>
</header>

<main>
  <!-- Filters -->
  <section class="card">
    <h2>Filtreler</h2>
    <form method="get" class="filters">
      <div class="filter-item">
        <label>Disiplin
          <select name="discipline">
            <option value="">Tümü</option>
            <?php foreach($disciplines as $d): ?>
              <option value="<?=e($d['name'])?>" <?=($discipline_filter==$d['name'])?'selected':''?>><?=e($d['name'])?></option>
            <?php endforeach; ?>
          </select>
        </label>
      </div>
      <div class="filter-item">
        <button type="submit">Filtrele</button>
      </div>
    </form>
  </section>

  <?php if(in_array($me['role'], ['Engineer', 'Manager'])): ?>
  <section class="card">
    <h2>Yeni Malzeme Ekle</h2>
    <form method="post" class="row">
      <input type="hidden" name="add_material" value="1">
      <label style="flex:1 1 150px">Kod
        <input name="code" placeholder="Malzeme kodu...">
      </label>
      <label style="flex:2 1 300px">Ad
        <input name="name" placeholder="Malzeme adı..." required>
      </label>
      <label style="flex:1 1 150px">Birim
        <input name="unit" placeholder="Adet, kg, m, vs..." required>
      </label>
      <label style="flex:1 1 150px">Disiplin
        <select name="discipline" required>
          <option value="">Seçiniz</option>
          <?php foreach($disciplines as $d): ?>
            <option value="<?=e($d['name'])?>"><?=e($d['name'])?></option>
          <?php endforeach; ?>
        </select>
      </label>
      <button style="align-self:flex-end">Ekle</button>
    </form>
  </section>
  <?php endif; ?>

  <section class="card">
    <h2>Malzeme Listesi</h2>
    <?php if($materials): ?>
    <table>
      <tr>
        <th>Kod</th>
        <th>Ad</th>
        <th>Birim</th>
        <th>Disiplin</th>
        <?php if(in_array($me['role'], ['Engineer', 'Manager'])): ?>
        <th>İşlem</th>
        <?php endif; ?>
      </tr>
      <?php foreach($materials as $material): ?>
      <tr>
        <td><?=e($material['code'] ?? '-')?></td>
        <td><?=e($material['name'])?></td>
        <td><?=e($material['unit'])?></td>
        <td><?=e($material['discipline'])?></td>
        <?php if(in_array($me['role'], ['Engineer', 'Manager'])): ?>
        <td>
          <button onclick="editMaterial(<?=$material['id']?>, '<?=e($material['code'] ?? '')?>', '<?=e($material['name'])?>', '<?=e($material['unit'])?>', '<?=e($material['discipline'])?>')">Düzenle</button>
          <form method="post" style="display:inline-block">
            <input type="hidden" name="id" value="<?=$material['id']?>">
            <input type="hidden" name="delete_material" value="1">
            <button type="submit" onclick="return confirm('Malzemeyi silmek istediğinizden emin misiniz?')">Sil</button>
          </form>
        </td>
        <?php endif; ?>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <p class="muted">Kayıtlı malzeme bulunamadı.</p>
    <?php endif; ?>
  </section>
</main>

<?php if(in_array($me['role'], ['Engineer', 'Manager'])): ?>
<!-- Edit Material Modal -->
<div id="editModal" class="modal">
  <div class="modal-content">
    <span class="close" onclick="closeModal()">&times;</span>
    <h2>Malzeme Düzenle</h2>
    <form method="post" id="editForm">
      <input type="hidden" name="id" id="editId">
      <input type="hidden" name="update_material" value="1">
      <label>Kod
        <input name="code" id="editCode" placeholder="Malzeme kodu...">
      </label>
      <label>Ad
        <input name="name" id="editName" placeholder="Malzeme adı..." required>
      </label>
      <label>Birim
        <input name="unit" id="editUnit" placeholder="Adet, kg, m, vs..." required>
      </label>
      <label>Disiplin
        <select name="discipline" id="editDiscipline" required>
          <option value="">Seçiniz</option>
          <?php foreach($disciplines as $d): ?>
            <option value="<?=e($d['name'])?>"><?=e($d['name'])?></option>
          <?php endforeach; ?>
        </select>
      </label>
      <button type="submit">Güncelle</button>
    </form>
  </div>
</div>

<script>
function editMaterial(id, code, name, unit, discipline) {
    document.getElementById('editId').value = id;
    document.getElementById('editCode').value = code;
    document.getElementById('editName').value = name;
    document.getElementById('editUnit').value = unit;
    document.getElementById('editDiscipline').value = discipline;
    document.getElementById('editModal').style.display = 'flex';
}

function closeModal() {
    document.getElementById('editModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('editModal');
    if (event.target == modal) {
        modal.style.display = 'none';
    }
}

// Sorting functionality
function sortTable(table, columnIndex, dataType) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.getAttribute('data-sort-order') !== 'asc';
    
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        let comparison = 0;
        
        if (dataType === 'number') {
            const aNum = parseFloat(aText) || 0;
            const bNum = parseFloat(bText) || 0;
            comparison = aNum - bNum;
        } else {
            comparison = aText.localeCompare(bText, 'tr', { numeric: true, sensitivity: 'base' });
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Toggle sort order
    table.setAttribute('data-sort-order', isAscending ? 'asc' : 'desc');
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update header indicators
    const headers = table.querySelectorAll('th');
    headers.forEach((header, index) => {
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
            if (index === columnIndex) {
                indicator.textContent = isAscending ? ' ↑' : ' ↓';
            } else {
                indicator.textContent = '';
            }
        }
    });
}

// Add double-click sorting to all table headers
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            // Add sort indicator
            const indicator = document.createElement('span');
            indicator.className = 'sort-indicator';
            indicator.style.marginLeft = '5px';
            header.appendChild(indicator);
            
            // Determine data type (simple heuristic)
            const firstRow = table.querySelector('tbody tr');
            let dataType = 'text';
            if (firstRow) {
                const cellText = firstRow.cells[index].textContent.trim();
                if (!isNaN(cellText) && cellText !== '') {
                    dataType = 'number';
                }
            }
            
            // Add double-click event
            header.addEventListener('dblclick', () => {
                sortTable(table, index, dataType);
            });
        });
    });
});
</script>
```

```
