<?php
// C:\workapp\app\public\request_view.php
require __DIR__.'/../inc/db.php';
require __DIR__.'/../inc/auth.php';
require __DIR__.'/../inc/helpers.php';
require_login();
$me = current_user();

$id = (int)($_GET['id'] ?? 0);
if(!$id){ echo "ID yok"; exit; }

$stmt = $pdo->prepare("SELECT r.*, s.label status_label, p.name priority_name,
  (SELECT display_name FROM Users WHERE id=r.assigned_engineer) AS assigned_engineer_name,
  (SELECT display_name FROM Users WHERE id=r.manager_id) AS manager_name,
  (SELECT display_name FROM Users WHERE id=r.created_by) AS creator_name
 FROM Requests r
 JOIN Status s ON s.id=r.status_id
 JOIN Priorities p ON p.id=r.priority_id
 WHERE r.id=?");
$stmt->execute([$id]);
$req = $stmt->fetch(PDO::FETCH_ASSOC);
if(!$req){ echo "Kayıt bulunamadı"; exit; }

/* Son log (geri al için) */
$lastLog = $pdo->prepare("SELECT * FROM StatusLog WHERE request_id=? ORDER BY id DESC LIMIT 1");
$lastLog->execute([$id]);
$last = $lastLog->fetch(PDO::FETCH_ASSOC);

/* POST işlemleri */
if ($_SERVER['REQUEST_METHOD']==='POST') {

  // Mühendis: NEW aşamasında değerlendirme yazıp yöneticinin onayına gönderir
  if (isset($_POST['to_pending_manager']) && $me['role']==='Engineer' && $req['status_id']==1) {
    $note = "Mühendis değerlendirmesi: ".trim($_POST['eng_note'] ?? '');
    $pdo->beginTransaction();
    // atanan mühendis ben olayım
    $pdo->prepare("UPDATE Requests SET status_id=3, assigned_engineer=? WHERE id=?")->execute([$me['id'],$id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],3,$me['id'],now(),$note]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: onayla (not opsiyonel)
  if (isset($_POST['approve']) && $me['role']==='Manager' && $req['status_id']==3) {
    $note = trim($_POST['mgr_note'] ?? '');
    $note = $note!=='' ? "Yönetici onayı: ".$note : "Yönetici onayı verildi";
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=4, manager_id=?, approved_at=? WHERE id=?")
         ->execute([$me['id'], now(), $id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],4,$me['id'],now(),$note]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: reddet (gerekçe zorunlu)
  if (isset($_POST['reject']) && $me['role']==='Manager' && $req['status_id']==3) {
    $reason = trim($_POST['mgr_reason'] ?? '');
    if ($reason==='') { $reason = 'Gerekçe verilmedi'; }
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=7 WHERE id=?")->execute([$id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],7,$me['id'],now(),'Yönetici reddetti: '.$reason]);
    $pdo->commit();
    redirect("/request_view.php?id=$id");
  }

  // Mühendis: işi başlat
  if (isset($_POST['start']) && $me['role']==='Engineer' && $req['status_id']==4) {
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=5, actual_start=? WHERE id=?")->execute([now(), $id]);
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],5,$me['id'],now(),'İş başlatıldı (Devam Ediyor)']);
    $pdo->commit(); redirect("/request_view.php?id=$id");
  }

  // Mühendis: işi tamamla
  if (isset($_POST['done']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $pdo->beginTransaction();
    $pdo->prepare("UPDATE Requests SET status_id=8 WHERE id=?")->execute([$id]); // Changed to status 8 (PENDING_COMPLETION)
    $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                   VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],8,$me['id'],now(),'İş tamamlama talebi oluşturuldu']); // Changed to status 8
    $pdo->commit(); redirect("/request_view.php?id=$id");
  }

  // Yönetici: işi tamamla (onayla)
  if (isset($_POST['approve_completion']) && $me['role']==='Manager' && $req['status_id']==8) { // New status 8 (PENDING_COMPLETION)
    $completion_note = trim($_POST['completion_note'] ?? '');
    $pdo->beginTransaction();
    try {
      // Update request status to DONE
      $pdo->prepare("UPDATE Requests SET status_id=6, actual_finish=? WHERE id=?")
          ->execute([now(), $id]);
      
      // Log the completion approval
      $note = 'Yönetici işi tamamladı olarak onayladı';
      if ($completion_note !== '') {
        $note .= ': ' . $completion_note;
      }
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id, 8, 6, $me['id'], now(), $note]);
      
      $pdo->commit();
    } catch (Exception $e) {
      $pdo->rollback();
      throw $e;
    }
    redirect("/request_view.php?id=$id");
  }

  // Yönetici: işi reddet
  if (isset($_POST['reject_completion']) && $me['role']==='Manager' && $req['status_id']==8) { // New status 8 (PENDING_COMPLETION)
    $rejection_reason = trim($_POST['rejection_reason'] ?? '');
    if ($rejection_reason==='') { $rejection_reason = 'Gerekçe verilmedi'; }
    $pdo->beginTransaction();
    try {
      // Revert request status back to IN_PROGRESS
      $pdo->prepare("UPDATE Requests SET status_id=5 WHERE id=?")->execute([$id]);
      
      // Log the rejection
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id, 8, 5, $me['id'], now(), 'Yönetici tamamlamayı reddetti: '.$rejection_reason]);
      
      $pdo->commit();
    } catch (Exception $e) {
      $pdo->rollback();
      throw $e;
    }
    redirect("/request_view.php?id=$id");
  }

  // Geri al: mevcut durumu bir önceki logdaki old_status_id'ye çevir
  if (isset($_POST['revert']) && $last && in_array($req['status_id'],[3,4])) {
    $prev = (int)$last['old_status_id'];
    if ($prev>0) {
      $pdo->beginTransaction();
      $pdo->prepare("UPDATE Requests SET status_id=? WHERE id=?")->execute([$prev,$id]);
      $pdo->prepare("INSERT INTO StatusLog(request_id,old_status_id,new_status_id,changed_by,changed_at,note)
                     VALUES(?,?,?,?,?,?)")->execute([$id,$req['status_id'],$prev,$me['id'],now(),'Geri alindi']);
      $pdo->commit();
    }
    redirect("/request_view.php?id=$id");
  }

  // Adam/saat
  if (isset($_POST['add_worker']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $stmt=$pdo->prepare("INSERT INTO WorkerAssignments(request_id,worker_id,date,hours,note) VALUES(?,?,?,?,?)");
    $stmt->execute([$id,(int)$_POST['worker_id'],$_POST['date'],(float)$_POST['hours'],trim($_POST['note'] ?? '')]);
    redirect("/request_view.php?id=$id#workers");
  }

  // Malzeme
  if (isset($_POST['add_material']) && $me['role']==='Engineer' && $req['status_id']==5) {
    $stmt=$pdo->prepare("INSERT INTO MaterialUsage(request_id,material_id,quantity,unit,note) VALUES(?,?,?,?,?)");
    $stmt->execute([$id,(int)$_POST['material_id'],(float)$_POST['quantity'],trim($_POST['unit']),trim($_POST['note'] ?? '')]);
    redirect("/request_view.php?id=$id#materials");
  }
}

/* Görünümler */
$workers = $pdo->query("SELECT id, full_name FROM Workers WHERE is_active=1 ORDER BY full_name")->fetchAll(PDO::FETCH_ASSOC);
$materials = $pdo->query("SELECT id, COALESCE(code||' - ','') || name AS label, unit FROM Materials ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

$logsStmt = $pdo->prepare("SELECT l.*, u.display_name FROM StatusLog l JOIN Users u ON u.id=l.changed_by WHERE l.request_id=? ORDER BY l.id DESC");
$logsStmt->execute([$id]); $history = $logsStmt->fetchAll(PDO::FETCH_ASSOC);

$wa = $pdo->prepare("SELECT a.date, w.full_name, a.hours, a.note
                     FROM WorkerAssignments a JOIN Workers w ON w.id=a.worker_id
                     WHERE a.request_id=? ORDER BY a.id DESC");
$wa->execute([$id]); $assignments = $wa->fetchAll(PDO::FETCH_ASSOC);

$mu = $pdo->prepare("SELECT m.name, COALESCE(m.code,'') AS code, u.quantity, u.unit, u.note
                     FROM MaterialUsage u JOIN Materials m ON m.id=u.material_id
                     WHERE u.request_id=? ORDER BY u.id DESC");
$mu->execute([$id]); $mat_used = $mu->fetchAll(PDO::FETCH_ASSOC);

// Get initial resource requirements
try {
    $initialWorkers = $pdo->prepare("SELECT r.estimated_hours, r.note, w.full_name 
                                    FROM RequestResources r 
                                    JOIN Workers w ON w.id = r.worker_id 
                                    WHERE r.request_id = ? AND r.worker_id IS NOT NULL");
    $initialWorkers->execute([$id]);
    $initial_worker_requirements = $initialWorkers->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Handle case where RequestResources table doesn't exist
    $initial_worker_requirements = [];
}

try {
    $initialMaterials = $pdo->prepare("SELECT r.estimated_quantity, r.unit, r.note, m.name, m.code 
                                      FROM RequestResources r 
                                      JOIN Materials m ON m.id = r.material_id 
                                      WHERE r.request_id = ? AND r.material_id IS NOT NULL");
    $initialMaterials->execute([$id]);
    $initial_material_requirements = $initialMaterials->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Handle case where RequestResources table doesn't exist
    $initial_material_requirements = [];
}
?>
<!doctype html>
<html lang="tr">
<head>
  <meta charset="utf-8">
  <title>İş #<?=$id?> • WorkApp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="/assets/css/workapp-modern.css">
  <style>
    .approval-section {
      background: var(--bg-primary);
      padding: var(--space-lg);
      border-radius: var(--radius-md);
      margin-bottom: var(--space-md);
      border: 1px solid var(--border-light);
    }

    .approval-title {
      margin: 0 0 var(--space-md);
      font-size: var(--font-size-base);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--space-sm);
    }

    .approval-title.success {
      color: var(--accent-success);
    }

    .approval-title.danger {
      color: var(--accent-danger);
    }

    .quick-nav {
      display: flex;
      gap: var(--space-sm);
      flex-wrap: wrap;
      margin-bottom: var(--space-xl);
    }

    .quick-nav a {
      padding: var(--space-sm) var(--space-md);
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-sm);
      text-decoration: none;
      color: var(--text-secondary);
      font-size: var(--font-size-sm);
      transition: var(--transition-base);
    }

    .quick-nav a:hover {
      background: var(--bg-tertiary);
      color: var(--accent-primary);
      transform: translateY(-1px);
    }
  </style>
</head>
<body>
<header class="header">
  <div class="container">
    <div class="header-content">
      <div class="header-nav">
        <a href="/requests.php">← Talepler</a>
        <?php if(in_array($me['role'],['Admin','Manager'])): ?>
        <a href="/reports.php">📊 Raporlar</a>
        <?php endif; ?>
      </div>
      <div class="header-title">🔧 #<?=$id?> • <?=e($req['title'])?></div>
      <div class="header-user">
        <span class="user-info">👋 <?=e($me['name'])?></span>
        <a href="/logout.php" class="logout-btn">🚪 Çıkış</a>
      </div>
    </div>
  </div>
</header>

<main class="main">
  <div class="container">
  <!-- Quick Navigation -->
  <div style="display:flex;gap:10px;flex-wrap:wrap;margin:16px 0">
    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
    <a href="#workers" style="padding:8px 12px;background:#0f172a;border:1px solid #1f2937;border-radius:8px;text-decoration:none">Adam/Saat</a>
    <a href="#materials" style="padding:8px 12px;background:#0f172a;border:1px solid #1f2937;border-radius:8px;text-decoration:none">Malzeme</a>
    <?php endif; ?>
    <a href="#log" style="padding:8px 12px;background:#0f172a;border:1px solid #1f2937;border-radius:8px;text-decoration:none">Geçmiş</a>
  </div>

  <!-- Request Summary Card -->
  <section class="card">
    <h2>Talep Özeti</h2>
    <div class="row" style="justify-content:space-between;align-items:flex-start">
      <div style="flex:1">
        <div><b>Başlık:</b> <?=e($req['title'])?></div>
        <div><b>Durum:</b> <?=e($req['status_label'])?></div>
        <div><b>Öncelik:</b> <?=e($req['priority_name'])?></div>
        <div><b>Birim:</b> <?=e($req['unit'])?></div>
        <div><b>Talep Sahibi:</b> <?=e($req['creator_name'])?></div>
        <?php if($req['call_number']): ?>
        <div><b>Çağrı Numarası:</b> <?=e($req['call_number'])?></div>
        <?php endif; ?>
      </div>
      <div style="flex:1">
        <?php if($req['planned_start'] || $req['planned_finish']): ?>
        <div><b>Planlanan Tarihler:</b> <?=e($req['planned_start'])?> - <?=e($req['planned_finish'])?></div>
        <?php endif; ?>
        <?php if($req['actual_start'] || $req['actual_finish']): ?>
        <div><b>Gerçek Tarihler:</b> <?=e($req['actual_start'])?> - <?=e($req['actual_finish'])?></div>
        <?php endif; ?>
        <?php if($req['assigned_engineer_name']): ?>
        <div><b>Atanan Mühendis:</b> <?=e($req['assigned_engineer_name'])?></div>
        <?php endif; ?>
        <?php if($req['manager_name']): ?>
        <div><b>Onaylayan Yönetici:</b> <?=e($req['manager_name'])?></div>
        <?php endif; ?>
      </div>
    </div>
    <?php if($req['description']): ?>
    <div style="margin-top:16px">
      <div><b>Açıklama:</b></div>
      <div class="muted" style="margin-top:6px;padding:10px;background:#1e293b;border-radius:8px"><?=nl2br(e($req['description']))?></div>
    </div>
    <?php endif; ?>
  </section>

  <?php 
  // Mühendis değerlendirmesini ve yönetici notlarını göster
  $engNote = '';
  $mgrNote = '';
  foreach($history as $log) {
    if (strpos($log['note'], 'Mühendis değerlendirmesi:') === 0) {
      $engNote = substr($log['note'], strlen('Mühendis değerlendirmesi: '));
    } elseif (strpos($log['note'], 'Yönetici onayı:') === 0) {
      $mgrNote = substr($log['note'], strlen('Yönetici onayı: '));
    } elseif ($log['new_status_id'] == 7 && strpos($log['note'], 'Yönetici reddetti:') === 0) {
      $mgrNote = substr($log['note'], strlen('Yönetici reddetti: '));
    }
  }
  if ($engNote || $mgrNote): ?>
  <section class="card">
    <h2>Değerlendirme Notları</h2>
    <?php if($engNote): ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px;margin-bottom:12px">
      <div style="color:#93c5fd;font-weight:bold">Mühendis Değerlendirmesi:</div>
      <div style="margin-top:6px"><?=nl2br(e($engNote))?></div>
    </div>
    <?php endif; ?>
    <?php if($mgrNote): ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px">
      <div style="color:#93c5fd;font-weight:bold">Yönetici <?=($req['status_id']==7)?'Reddetme':'Onay'?> Notu:</div>
      <div style="margin-top:6px"><?=nl2br(e($mgrNote))?></div>
    </div>
    <?php endif; ?>
  </section>
  <?php endif; ?>

  <?php if (!empty($initial_worker_requirements) || !empty($initial_material_requirements)): ?>
  <section class="card">
    <h2>Planlanan Kaynak Gereksinimleri</h2>
    
    <?php if (!empty($initial_worker_requirements)): ?>
    <h3>Gerekli Çalışanlar</h3>
    <table>
      <tr><th>Çalışan</th><th>Tahmini Saat</th><th>Not</th></tr>
      <?php foreach($initial_worker_requirements as $worker): ?>
      <tr>
        <td><?=e($worker['full_name'])?></td>
        <td><?=e($worker['estimated_hours'])?></td>
        <td><?=e($worker['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php endif; ?>
    
    <?php if (!empty($initial_material_requirements)): ?>
    <h3 style="margin-top:20px">Gerekli Malzemeler</h3>
    <table>
      <tr><th>Kod</th><th>Malzeme</th><th>Tahmini Miktar</th><th>Birim</th><th>Not</th></tr>
      <?php foreach($initial_material_requirements as $material): ?>
      <tr>
        <td><?=e($material['code'] ?? '-')?></td>
        <td><?=e($material['name'])?></td>
        <td><?=e($material['estimated_quantity'])?></td>
        <td><?=e($material['unit'])?></td>
        <td><?=e($material['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php endif; ?>
  </section>
  <?php endif; ?>

  <!-- Workflow Actions Section -->
  <section class="card">
    <h2>İş Akışı İşlemleri</h2>
    <div class="btns">

    <?php if($me['role']==='Engineer' && $req['status_id']==1): ?>
      <!-- Mühendis değerlendirmesi -->
      <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
        <h3 style="margin-top:0;color:#93c5fd">Mühendis Değerlendirmesi</h3>
        <form method="post" class="row" style="gap:12px">
          <input type="hidden" name="to_pending_manager" value="1">
          <label style="flex:1 1 100%">Değerlendirme Notu
            <textarea name="eng_note" placeholder="Yapılabilirlik, ön koşullar, riskler..." style="min-height:80px"></textarea>
          </label>
          <div style="flex:0 0 100%;display:flex;justify-content:flex-end">
            <button style="background:#0ea5e9">Yönetici Onayına Gönder</button>
          </div>
        </form>
      </div>
    <?php endif; ?>

    <?php if($me['role']==='Manager' && $req['status_id']==3): ?>
      <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
        <h3 style="margin-top:0;color:#93c5fd">Yönetici Onayı</h3>

        <!-- Onay Formu -->
        <div class="approval-section">
          <h4 class="approval-title success">✓ Onaylama</h4>
          <form method="post" class="form">
            <div class="form-group">
              <label class="form-label">Onay Notu (opsiyonel)</label>
              <textarea class="form-input" name="mgr_note" placeholder="Onay notu (opsiyonel)" style="min-height:60px;resize:vertical"></textarea>
            </div>
            <div class="form-actions">
              <button name="approve" class="btn btn-success">✓ Onayla</button>
            </div>
          </form>
        </div>

        <!-- Red Formu -->
        <div class="approval-section">
          <h4 class="approval-title danger">✗ Reddetme</h4>
          <form method="post" class="form">
            <div class="form-group">
              <label class="form-label">Reddetme Gerekçesi (zorunlu)</label>
              <textarea class="form-input" name="mgr_reason" required placeholder="Red gerekçesi" style="min-height:60px;resize:vertical"></textarea>
            </div>
            <div class="form-actions">
              <button name="reject" class="btn btn-danger">✗ Reddet</button>
            </div>
          </form>
        </div>
      </div>
    <?php endif; ?>

    <?php if($me['role']==='Engineer' && $req['status_id']==4): ?>
      <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
        <h3 style="margin-top:0;color:#93c5fd">İş Başlatma</h3>
        <form method="post">
          <p>Bu işi başlatmak ve "Devam Ediyor" durumuna almak istiyor musunuz?</p>
          <div style="display:flex;justify-content:flex-end">
            <button name="start" style="background:#0ea5e9">İşi Başlat (Devam Ediyor)</button>
          </div>
        </form>
      </div>
    <?php endif; ?>

    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
      <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
        <h3 style="margin-top:0;color:#93c5fd">İş Tamamlama</h3>
        <form method="post">
          <p>Bu işi tamamlamak ve yönetici onayı için göndermek istiyor musunuz?</p>
          <div style="display:flex;justify-content:flex-end;gap:10px">
            <a href="/request_edit.php?id=<?=$id?>" style="padding:10px 14px;background:#0ea5e9;color:white;border-radius:10px;text-decoration:none">Kaynakları Güncelle</a>
            <button name="done" style="background:#22c55e">Tamamla ve Onaya Gönder</button>
          </div>
        </form>
      </div>
    <?php endif; ?>

    <?php if($me['role']==='Manager' && $req['status_id']==8): // New status 8 (PENDING_COMPLETION) ?>
      <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
        <h3 style="margin-top:0;color:#93c5fd">İş Tamamlama Onayı</h3>
        <p style="color:#94a3b8;margin-bottom:16px">Bu iş mühendis tarafından tamamlandı olarak işaretlendi. Lütfen inceleyip onaylayın veya reddedin.</p>

        <!-- Tamamlama Onay Formu -->
        <div style="background:#0f172a;padding:16px;border-radius:8px;margin-bottom:16px;border:1px solid #334155">
          <h4 style="margin:0 0 12px;color:#22c55e;font-size:16px">✓ Tamamlamayı Onayla</h4>
          <form method="post" class="row">
            <label style="flex:1 1 100%">Onay Notu (İsteğe Bağlı)
              <textarea name="completion_note" placeholder="Onay notu (isteğe bağlı)" style="min-height:60px;resize:vertical"></textarea>
            </label>
            <div style="flex:0 0 100%;display:flex;justify-content:flex-end;margin-top:12px">
              <button name="approve_completion" style="background:#22c55e;padding:12px 20px">İşi Tamamlandı Olarak Onayla</button>
            </div>
          </form>
        </div>

        <!-- Tamamlama Red Formu -->
        <div style="background:#0f172a;padding:16px;border-radius:8px;border:1px solid #334155">
          <h4 style="margin:0 0 12px;color:#ef4444;font-size:16px">✗ Tamamlamayı Reddet</h4>
          <form method="post" class="row">
            <label style="flex:1 1 100%">Reddetme Gerekçesi (Zorunlu)
              <textarea name="rejection_reason" placeholder="Reddetme gerekçesi" required style="min-height:60px;resize:vertical"></textarea>
            </label>
            <div style="flex:0 0 100%;display:flex;justify-content:flex-end;margin-top:12px">
              <button name="reject_completion" style="background:#ef4444;padding:12px 20px">Reddet ve Geri Gönder</button>
            </div>
          </form>
        </div>
      </div>
    <?php endif; ?>

    <?php if(in_array($req['status_id'],[3,4])): ?>
      <div style="background:#1e293b;padding:16px;border-radius:8px">
        <h3 style="margin-top:0;color:#93c5fd">Geri Alma</h3>
        <form method="post">
          <p>Bu işlem talebi önceki durumuna geri alacaktır.</p>
          <div style="display:flex;justify-content:flex-end">
            <button name="revert" style="background:#f59e0b">Geri Al (Önceki Duruma)</button>
          </div>
        </form>
      </div>
    <?php endif; ?>

    </div>
  </section>

  <!-- Worker Hours Section -->
  <section class="card" id="workers">
    <h2>Gerçek Adam/Saat Kullanımı</h2>
    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
    <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
      <h3 style="margin-top:0;color:#93c5fd">Yeni Adam/Saat Girişi</h3>
      <form method="post" class="row">
        <input type="hidden" name="add_worker" value="1">
        <label style="flex:1 1 160px">Tarih <input type="date" name="date" required value="<?=date('Y-m-d')?>"></label>
        <label style="flex:2 1 260px">Çalışan
          <select name="worker_id">
            <?php foreach($workers as $w): ?><option value="<?=$w['id']?>"><?=e($w['full_name'])?></option><?php endforeach; ?>
          </select>
        </label>
        <label style="flex:1 1 120px">Saat <input type="number" step="0.25" min="0" name="hours" required></label>
        <label style="flex:2 1 260px">Not <input name="note"></label>
        <div style="flex:0 0 100%;display:flex;justify-content:flex-end;margin-top:12px">
          <button>Ekle</button>
        </div>
      </form>
    </div>
    <?php else: ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px;margin-bottom:16px">
      <i class="muted">Adam/saat girişi yalnızca <b>Devam Ediyor</b> durumunda yapılabilir.</i>
    </div>
    <?php endif; ?>

    <?php if($assignments): ?>
    <h3>Kayıtlı Adam/Saat Kullanımları</h3>
    <table>
      <tr><th>Tarih</th><th>Çalışan</th><th>Saat</th><th>Not</th></tr>
      <?php foreach($assignments as $r): ?>
      <tr><td><?=e($r['date'])?></td><td><?=e($r['full_name'])?></td><td><?=e($r['hours'])?></td><td><?=e($r['note'])?></td></tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px">
      <i class="muted">Henüz adam/saat kaydı girilmemiş.</i>
    </div>
    <?php endif; ?>
  </section>

  <!-- Material Usage Section -->
  <section class="card" id="materials">
    <h2>Gerçek Malzeme Kullanımı</h2>
    <?php if($me['role']==='Engineer' && $req['status_id']==5): ?>
    <div style="background:#1e293b;padding:16px;border-radius:8px;margin-bottom:16px">
      <h3 style="margin-top:0;color:#93c5fd">Yeni Malzeme Kullanımı</h3>
      <form method="post" class="row">
        <input type="hidden" name="add_material" value="1">
        <label style="flex:2 1 260px">Malzeme
          <select name="material_id">
            <?php foreach($materials as $m): ?><option value="<?=$m['id']?>"><?=e($m['label'])?></option><?php endforeach; ?>
          </select>
        </label>
        <label style="flex:1 1 140px">Miktar <input type="number" step="0.01" min="0" name="quantity" required></label>
        <label style="flex:1 1 120px">Birim <input name="unit" value="<?=e($materials[0]['unit'] ?? '')?>" required></label>
        <label style="flex:2 1 260px">Not <input name="note"></label>
        <div style="flex:0 0 100%;display:flex;justify-content:flex-end;margin-top:12px">
          <button>Ekle</button>
        </div>
      </form>
    </div>
    <?php else: ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px;margin-bottom:16px">
      <i class="muted">Malzeme girişi yalnızca <b>Devam Ediyor</b> durumunda yapılabilir.</i>
    </div>
    <?php endif; ?>

    <?php if($mat_used): ?>
    <h3>Kayıtlı Malzeme Kullanımları</h3>
    <table>
      <tr><th>Kod</th><th>Malzeme</th><th>Miktar</th><th>Birim</th><th>Not</th></tr>
      <?php foreach($mat_used as $r): ?>
      <tr>
        <td><?=e($r['code'])?></td>
        <td><?=e($r['name'])?></td>
        <td><?=e($r['quantity'])?></td>
        <td><?=e($r['unit'])?></td>
        <td><?=e($r['note'])?></td>
      </tr>
      <?php endforeach; ?>
    </table>
    <?php else: ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px">
      <i class="muted">Henüz malzeme kullanımı kaydedilmemiş.</i>
    </div>
    <?php endif; ?>
  </section>

  <!-- Status Log Section -->
  <section class="card" id="log">
    <h2>Durum Geçmişi</h2>
    <?php if($history): ?>
      <ul style="margin:6px 0 0 18px">
        <?php foreach($history as $h): ?>
          <li style="margin-bottom:8px;padding:8px;background:#1e293b;border-radius:6px">
            <div><b><?=e($h['changed_at'])?></b> — <?=e($h['display_name'])?></div>
            <div style="margin-top:4px;color:#cbd5e1"><?=e($h['note'])?></div>
          </li>
        <?php endforeach; ?>
      </ul>
    <?php else: ?>
    <div style="background:#1e293b;padding:12px;border-radius:8px">
      <i class="muted">Günlük kaydı yok.</i>
    </div>
    <?php endif; ?>
  </section>
</main>

<script>
// Date copy/paste functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to all date input fields
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // Handle copy event
        input.addEventListener('copy', function(e) {
            // We don't need to modify the copy behavior as the date value is already in YYYY-MM-DD format
            // The browser handles this correctly
        });
        
        // Handle paste event
        input.addEventListener('paste', function(e) {
            // Prevent default paste behavior
            e.preventDefault();
            
            // Get pasted text
            let pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // If pasted text is a full date string (e.g., "2023-12-25"), use it directly
            if (pastedText.match(/^\d{4}-\d{2}-\d{2}$/)) {
                this.value = pastedText;
            } 
            // If pasted text is in another common format, try to parse it
            else if (pastedText.match(/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}$/)) {
                // Try to parse MM/DD/YYYY or DD/MM/YYYY or YYYY-MM-DD formats
                let parts = pastedText.split(/[-\/]/);
                if (parts.length === 3) {
                    let year, month, day;
                    
                    // Assume first part is month if it's <= 12 and second part is day if it's <= 31
                    if (parts[0].length === 4) {
                        // YYYY-MM-DD format
                        year = parts[0];
                        month = parts[1].padStart(2, '0');
                        day = parts[2].padStart(2, '0');
                    } else if (parseInt(parts[0]) <= 12 && parseInt(parts[1]) <= 31) {
                        // MM/DD/YYYY format
                        month = parts[0].padStart(2, '0');
                        day = parts[1].padStart(2, '0');
                        year = parts[2];
                    } else {
                        // DD/MM/YYYY format
                        day = parts[0].padStart(2, '0');
                        month = parts[1].padStart(2, '0');
                        year = parts[2];
                    }
                    
                    // Validate and format as YYYY-MM-DD
                    if (year && month && day && 
                        parseInt(month) >= 1 && parseInt(month) <= 12 && 
                        parseInt(day) >= 1 && parseInt(day) <= 31) {
                        this.value = `${year}-${month}-${day}`;
                    }
                }
            }
            // If it's just a year or partial date, we could handle that too, but for now we'll just use as-is
            else {
                // For other cases, just paste the text (browser will handle validation)
                this.value = pastedText;
            }
        });
    });
});
</script>
</body>
</html>