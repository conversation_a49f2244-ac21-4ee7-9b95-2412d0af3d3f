<?php
// <PERSON><PERSON><PERSON> to add the PENDING_COMPLETION status to the database
require __DIR__.'/../inc/db.php';

// Only allow this script to run if a specific parameter is passed
if (!isset($_GET['run'])) {
    echo "<h2>Status Update Script</h2>";
    echo "<p>This script will add the 'PENDING_COMPLETION' status to the database.</p>";
    echo "<p><a href='?run=1'>Click here to run the script</a></p>";
    exit;
}

try {
    // Check if the status already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM Status WHERE id = 8");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // Insert the new status
        $pdo->prepare("INSERT INTO Status(id,code,label) VALUES(8,'PENDING_COMPLETION','Tamamlama Onayı Bekliyor')")->execute();
        echo "New status 'PENDING_COMPLETION' added successfully.<br>";
    } else {
        echo "Status 'PENDING_COMPLETION' already exists.<br>";
    }
    
    // Display all statuses
    echo "<h3>Current Statuses:</h3>";
    $stmt = $pdo->query("SELECT * FROM Status ORDER BY id");
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Code</th><th>Label</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr><td>" . $status['id'] . "</td><td>" . $status['code'] . "</td><td>" . $status['label'] . "</td></tr>";
    }
    echo "</table>";
    
    echo "<p><a href='/requests.php'>Return to main application</a></p>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>